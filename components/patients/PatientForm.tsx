import React, { useLayoutEffect } from 'react';
import { ScrollView, View, Pressable } from 'react-native';
import { useForm, Resolver, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import ControlledInput from '../ControlledInput'; 
import RouterSelectInput, { RouterSelectOption } from '../common/RouterSelectInput';
import { Button } from '../ui/Button';
import { patientFormSchema, PatientFormData } from '@/schema/patient';
import { Patient, usePatients } from '@/hooks/entities/usePatients';
import { Constants } from '@/types/database.types';
import { useAppAlerts } from '@/hooks/useAppAlerts';

// Prepare options for the blood type select
const bloodTypeOptions: RouterSelectOption[] = [
  // Add an option for 'Unknown' or similar to represent null/empty
  { id: '', label: 'Unknown / Not specified' },
  ...Constants.public.Enums.blood_type_enum.map(type => ({ id: type, label: type }))
];

interface PatientFormProps {
  initialValues?: Patient; // Use Patient type for initial values
  onSubmit: (data: PatientFormData) => void;
  isLoading?: boolean;
  isEditing: boolean;
  patientId?: string; // For delete action and to know if delete UI should be shown
}

const PatientForm: React.FC<PatientFormProps> = ({
  initialValues,
  onSubmit,
  isLoading = false,
  isEditing,
  patientId,
}) => {
  const navigation = useNavigation();
  const router = useRouter();
  const { confirm, showSuccess, showError } = useAppAlerts();
  const { useDeletePatient } = usePatients();
  const deletePatientMutation = useDeletePatient();

  const { control, handleSubmit } = useForm<PatientFormData>({
    // Explicitly cast the resolver type
    resolver: zodResolver(patientFormSchema) as Resolver<PatientFormData>,
    defaultValues: initialValues ? {
      name: initialValues.name,
      age: initialValues.age ?? undefined,
      // Default to empty string if bloodtype is null/undefined for the Select component
      bloodtype: initialValues.bloodtype ?? '',
    } : {
      name: '',
      age: undefined,
      bloodtype: '', // Default to empty string for Select
    },
  });

  const handleDeletePress = () => {
    if (!patientId) return;

    confirm({
      title: 'Confirm Deletion',
      message: 'Are you sure you want to delete this patient? This action cannot be undone.',
      confirmText: 'Delete',
      style: 'destructive',
      onConfirm: async () => {
        try {
          await deletePatientMutation.mutateAsync({ id: patientId });
          showSuccess('Patient has been deleted.', 'Deleted');
          router.dismiss(); 
        } catch (error: any) {
          console.error('Delete Error from PatientForm:', error);
          showError(error.message || 'Failed to delete patient.');
        }
      },
    });
  };

  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditing ? 'Edit Patient' : 'Add Patient',
      headerRight: () => (
        isEditing && patientId && (
          <Pressable onPress={handleDeletePress} disabled={deletePatientMutation.isPending} style={{ marginRight: 15 }}>
            <Ionicons
              name="trash-outline"
              size={24}
              color={deletePatientMutation.isPending ? 'gray' : 'red'} 
            />
          </Pressable>
        )
      ),
    });
  }, [navigation, isEditing, patientId, deletePatientMutation.isPending, confirm, showError, showSuccess, router]);

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <ScrollView 
        className="flex-1 px-4" 
        contentContainerStyle={{ paddingVertical: 24 }}
        keyboardShouldPersistTaps="handled"
      >
        {/* Name - ControlledInput */}
        <ControlledInput
          control={control}
          name="name"
          label="Full Name"
          placeholder="Enter patient's full name"
          leftIcon="person"
          autoCapitalize="words"
        />

        {/* Age - ControlledInput */}
        <ControlledInput
          control={control}
          name="age"
          label="Age (Optional)"
          placeholder="Enter patient's age"
          leftIcon="calendar-outline"
          keyboardType="number-pad"
        />

        {/* Blood Type */}
        <Controller
          control={control}
          name="bloodtype"
          render={({ field, fieldState: { error } }) => (
            <RouterSelectInput
              label="Blood Type (Optional)"
              options={bloodTypeOptions}
              selectedOption={bloodTypeOptions.find(opt => opt.id === field.value) || undefined}
              onSelect={(option) => field.onChange(option ? option.id : '')}
              placeholder="Select blood type"
              leftIcon="water-outline"
              modalTitle="Select Blood Type"
              error={error?.message}
              showSearchInModal={false}
            />
          )}
        />
      </ScrollView>
      <View className="px-4 py-3 border-t border-border dark:border-neutral-700 bg-background dark:bg-neutral-900">
        <Button
          title={isEditing ? 'Update Patient' : 'Create Patient'}
          onPress={handleSubmit(onSubmit)}
          isLoading={isLoading}
          icon={isEditing ? "save" : "add-circle"}
          fullWidth
        />
      </View>
    </View>
  );
};

export default PatientForm; 