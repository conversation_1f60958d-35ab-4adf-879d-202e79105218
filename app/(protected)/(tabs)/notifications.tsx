import { Text, View, ActivityIndicator, useColorScheme } from 'react-native'
import React, { useMemo, useContext } from 'react'
import { CalendarProvider, AgendaList, ExpandableCalendar, AgendaListProps, CalendarContext } from 'react-native-calendars'
import { MarkedDates , Theme } from 'react-native-calendars/src/types'
import { useMedicines, MedicineWithRelations } from '@/hooks/entities/useMedicines'
import { format } from 'date-fns'
import colors from 'tailwindcss/colors';

interface AgendaItem {
  title: string
  data: MedicineWithRelations[]
}

// Separate component to consume context, ensuring it re-renders when date changes
function FilteredAgenda() {
  const { useFetchMedicines } = useMedicines()
  const { data: medicines, isLoading, isError, error } = useFetchMedicines()
  const context = useContext(CalendarContext) // Get the full context
  const selectedDate = context.date // Get the currently selected date string (YYYY-MM-DD) from context
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Process all medicine data (memoized based on medicines)
  const allAgendaItems = useMemo(() => {
    const items: { [key: string]: MedicineWithRelations[] } = {}
    if (medicines) {
      medicines.forEach((med) => {
        if (med.expiration_date) {
          const expirationDateString = format(new Date(med.expiration_date), 'yyyy-MM-dd')
          if (!items[expirationDateString]) {
            items[expirationDateString] = []
          }
          items[expirationDateString].push(med)
        }
      })
    }
    // Convert grouped items to AgendaList format and sort by date
    const agendaSections: AgendaItem[] = Object.keys(items)
      .sort() // Sort dates chronologically
      .map(d => ({
        title: d,
        data: items[d],
      }))
    return agendaSections
  }, [medicines])

  // Filter items based on the context date (memoized based on allAgendaItems and context date)
  const filteredAgendaItems = useMemo(() => {
    if (!selectedDate) return [] // Handle case where date might be initially null
    return allAgendaItems.filter(section => section.title === selectedDate)
  }, [allAgendaItems, selectedDate])

  const renderItem: AgendaListProps['renderItem'] = ({ item }) => {
    return (
      <View className="p-4 my-2 mr-4 bg-card dark:bg-neutral-800 rounded-lg shadow border border-border dark:border-neutral-700">
        <Text className="font-semibold text-card-foreground dark:text-neutral-100">{item.name}</Text>
        {item.description && <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1">{item.description}</Text>}
        <Text className="text-xs text-destructive dark:text-red-400 mt-1">
          Expires on: {item.expiration_date ? format(new Date(item.expiration_date), 'PPP') : 'N/A'}
        </Text>
        {item.patients && <Text className="text-xs text-muted-foreground dark:text-neutral-500 mt-1">Patient: {item.patients.name}</Text>}
      </View>
    )
  }

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center mt-10 bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-2 text-muted-foreground dark:text-neutral-400">Loading medicines...</Text>
      </View>
    )
  }

  if (isError) {
    return (
      <View className="flex-1 justify-center items-center p-4 mt-10 bg-background dark:bg-neutral-900">
        <Text className="text-destructive dark:text-red-400">Error loading medicines:</Text>
        <Text className="text-destructive dark:text-red-400">{error?.message}</Text>
      </View>
    )
  }

  return (
    <AgendaList
      sections={filteredAgendaItems} // Use the filtered items
      renderItem={renderItem}
      sectionStyle={{ marginBottom: 10 }}
      style={{ backgroundColor: isDark ? colors.neutral[900] : colors.white }}
      ListEmptyComponent={
        <View className="flex-1 justify-center items-center mt-10 p-4">
          {/* Check if there are items for *any* date before saying none found */}
          {allAgendaItems.length > 0 ? (
            <Text className="text-muted-foreground dark:text-neutral-400 text-center">
              No expiring medicines on {format(new Date(selectedDate || Date.now()), 'MMM d, yyyy')}.
            </Text>
          ) : (
            <Text className="text-muted-foreground dark:text-neutral-400 text-center">No expiring medicines found.</Text>
          )}
        </View>
      }
    />
  )
}

export default function NotificationsScreen() {
  const { useFetchMedicines } = useMedicines()
  const { data: medicines } = useFetchMedicines() // Fetch here mainly for marked dates
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const today = new Date()
  const todayString = format(today, 'yyyy-MM-dd')

  // Define theme colors based on scheme
  const primaryColor = colors.blue[600];
  const primaryForegroundColor = colors.neutral[50];
  const foregroundColor = isDark ? colors.neutral[100] : colors.neutral[950];
  const mutedForegroundColor = isDark ? colors.neutral[400] : colors.neutral[500];
  const backgroundColor = isDark ? colors.neutral[900] : colors.white;
  const cardBackgroundColor = isDark ? colors.neutral[800] : colors.white; // Slightly different for calendar internal bg?
  const borderColor = isDark ? colors.neutral[700] : colors.neutral[200];
  const destructiveColor = colors.red[500]; // Or 600? Using 500 for dot.
  const warningColor = colors.orange[500]; // For expiration dot

  // Calendar theme object
  const calendarTheme: Theme = {
    calendarBackground: backgroundColor,
    dayTextColor: foregroundColor,
    textDisabledColor: mutedForegroundColor,
    monthTextColor: foregroundColor,
    textSectionTitleColor: mutedForegroundColor,
    arrowColor: primaryColor,
    todayTextColor: primaryColor,
    selectedDayBackgroundColor: primaryColor,
    selectedDayTextColor: primaryForegroundColor,
    agendaKnobColor: primaryColor, // Added knob color
    // Add other theme properties as needed...
  };

  // Calculate marked dates (only needs medicines data)
  const markedDates = useMemo(() => {
    const marked: MarkedDates = {}
    if (medicines) {
      medicines.forEach((med) => {
        if (med.expiration_date) {
          const expirationDateString = format(new Date(med.expiration_date), 'yyyy-MM-dd')
          marked[expirationDateString] = {
            marked: true,
            dotColor: warningColor, // Use themed warning color
          }
        }
      })
    }
    // Mark today's date
    marked[todayString] = {
      ...(marked[todayString] || {}),
      selected: true,
      // selectedColor is handled by theme.selectedDayBackgroundColor
      // selectedTextColor is handled by theme.selectedDayTextColor
    }
    return marked
  }, [medicines, todayString, warningColor])

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      {/* CalendarProvider wraps both Calendar and the new Agenda component */}
      <CalendarProvider
        date={todayString}
        showTodayButton
        theme={{
          todayButtonTextColor: primaryColor,
        }}
      >
        <ExpandableCalendar
          markedDates={markedDates}
          theme={calendarTheme}
        />
        {/* Render the separate component which uses the context */}
        <FilteredAgenda />
      </CalendarProvider>
    </View>
  )
}