import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { FlashList } from '@shopify/flash-list';
import { Ionicons } from '@expo/vector-icons';
import { usePatients } from '@/hooks/entities/usePatients';
import PatientCard from '@/components/patients/PatientCard';
import { Button } from '@/components/ui/Button';


/**
 * Patients screen showing a list of the user's patients (within doctors-tabs)
 */
export default function PatientsScreen() {
  const { useFetchPatients } = usePatients();
  const { data: patients, isLoading, isError, refetch } = useFetchPatients();

  // Navigate to the modal screen for viewing/editing
  const handlePressPatient = (id: string) => {
      router.push({ pathname: '/modals/modal-patient', params: { id } });
  };

  // Navigate to the modal screen for creation
  const handleCreate = () => {
    router.push('/modals/modal-patient');
  };

  // Show loading state
  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">Loading patients...</Text>
      </View>
    );
  }

  // Show error state
  if (isError) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="alert-circle" size={48} className="text-destructive dark:text-red-400" />
        <Text className="text-destructive dark:text-red-400 mt-4 mb-4 text-center">
          Failed to load patients
        </Text>
        <Button title="Try Again" onPress={() => refetch()} variant="primary" />
      </View>
    );
  }

  // Show empty state
  if (!patients || patients.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="people-outline" size={48} className="text-muted-foreground dark:text-neutral-500" />
        <Text className="text-muted-foreground dark:text-neutral-400 mt-4 mb-2 text-center font-medium text-lg">
          No Patients Yet
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-4 text-center px-6">
          Add patient profiles to manage their information.
        </Text>
        <Button title="Add First Patient" onPress={handleCreate} variant="primary" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
    
      <FlashList
        data={patients}
        renderItem={({ item }) => (
          <PatientCard
            patient={item}
            onPress={() => handlePressPatient(item.id)}
          />
        )}
        estimatedItemSize={80} // Adjust estimated size as needed
        contentContainerClassName="p-4"
        ItemSeparatorComponent={() => <View className="h-3" />}
      />

    </View>
  );
}