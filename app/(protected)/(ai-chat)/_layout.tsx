import React from 'react';
import { View, Text, FlatList, Pressable, ActivityIndicator } from 'react-native';
import { Drawer } from 'expo-router/drawer';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useLabResultAI } from '@/hooks/useLabResultAI';

// Custom drawer content showing previous chat sessions
function CustomDrawerContent() {
  const { useFetchAllChatSessions } = useLabResultAI();
  const { data: chatSessions = [], isLoading } = useFetchAllChatSessions();

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900 pt-12">
      <View className="px-4 py-6 border-b border-border dark:border-neutral-700">
        <Text className="text-lg font-semibold text-foreground dark:text-neutral-200">
          AI Chat Sessions
        </Text>
        <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1">
          Previous lab result conversations
        </Text>
      </View>

      {isLoading ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" className="text-primary" />
          <Text className="mt-4 text-muted-foreground dark:text-neutral-400">
            Loading chat sessions...
          </Text>
        </View>
      ) : (
        <FlatList
          data={chatSessions}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <Pressable
              onPress={() => router.push({
                pathname: '/(ai-chat)/lab-result-ai-chat' as any,
                params: { labResultId: item.labResultId }
              })}
              className="px-4 py-3 border-b border-border/50 dark:border-neutral-700/50 active:bg-muted dark:active:bg-neutral-800"
            >
              <View className="flex-row items-center">
                <Ionicons name="document-text" size={20} className="text-primary dark:text-sky-400 mr-3" />
                <View className="flex-1">
                  <Text className="font-medium text-foreground dark:text-neutral-200">
                    {item.labName}
                  </Text>
                  <Text className="text-sm text-muted-foreground dark:text-neutral-400">
                    {item.lastMessage}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={16} className="text-muted-foreground dark:text-neutral-500" />
              </View>
            </Pressable>
          )}
          ListEmptyComponent={
            <View className="px-4 py-8 items-center">
              <Ionicons name="chatbubbles-outline" size={48} className="text-muted-foreground dark:text-neutral-500 mb-4" />
              <Text className="text-muted-foreground dark:text-neutral-400 text-center">
                No chat sessions yet
              </Text>
              <Text className="text-sm text-muted-foreground dark:text-neutral-500 text-center mt-2">
                Analyze a lab result to start chatting with AI
              </Text>
            </View>
          }
        />
      )}
    </View>
  );
}

const DrawerLayout = () => {
  return (
    <Drawer
      drawerContent={CustomDrawerContent}
      screenOptions={{
        drawerStyle: {
          width: 300,
        },
        headerShown: true,
      }}
    >
      <Drawer.Screen
        name="lab-result-ai-chat"
        options={{
          title: 'AI Lab Analysis',
          drawerLabel: 'Current Chat',
        }}
      />
    </Drawer>
  );
};

export default DrawerLayout;