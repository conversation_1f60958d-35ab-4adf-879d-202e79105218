import React from 'react';
import { View, Text } from 'react-native';
import { format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import Card from '../ui/Card'; // Assuming Card component exists in ui
import { PrescriptionWithDetails } from '@/hooks/entities/usePrescriptions';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';

interface PrescriptionCardProps {
  prescription: PrescriptionWithDetails;
  onPress?: () => void;
}

const PrescriptionCard: React.FC<PrescriptionCardProps> = ({ prescription, onPress }) => {
  const formattedDate = prescription.prescription_date
    ? format(new Date(prescription.prescription_date), 'MMM d, yyyy')
    : 'No date';

  const imagePath = prescription.front_image_path;
  const bucketId = prescription.image_bucket_id;

  console.log(`PrescriptionCard (${prescription.name}): imagePath:`, imagePath, 'bucketId:', bucketId);

  const { url: thumbnailUrl, isLoading: isLoadingThumbnail, error: urlError } = useSupabaseStorageUrl({
    path: imagePath,
    bucketId: bucketId, 
  });
  
  console.log(`PrescriptionCard (${prescription.name}): thumbnailUrl:`, thumbnailUrl, 'isLoading:', isLoadingThumbnail, 'urlError:', urlError);

  const hasAnyImage = !!prescription.front_image_path || !!prescription.back_image_path;

  const content = (
    <View className="space-y-2">
      {/* Date and attachment indicator */}
      <View className="flex-row justify-between items-center">
        <Text className="text-sm text-gray-500">{formattedDate}</Text>
        {hasAnyImage && (
          <View className="flex-row items-center">
            <Ionicons name="camera-outline" size={16} color="#6B7280" />
            <Text className="text-sm text-gray-500 ml-1">Image(s)</Text>
          </View>
        )}
      </View>

      {/* Patient info if available */}
      {prescription.patients && (
        <View className="flex-row items-center mt-1">
          <Ionicons name="person-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">Patient: {prescription.patients.name}</Text>
        </View>
      )}

      {/* Doctor info if available */}
      {prescription.user_doctors && (
        <View className="flex-row items-center mt-1">
          <Ionicons name="medical-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">Doctor: {prescription.user_doctors.name}</Text>
        </View>
      )}

      {/* Notes preview if available */}
      {prescription.notes && (
         <Text className="text-sm text-gray-600 mt-1" numberOfLines={2}>
           Notes: {prescription.notes}
         </Text>
      )}
    </View>
  );

  return (
    <Card
      title={prescription.name}
      content={content}
      onPress={onPress}
      imageUrl={thumbnailUrl} // Pass the fetched URL
      imageLoading={isLoadingThumbnail} // Pass the loading state
      rightAction={
        <Ionicons name="chevron-forward" size={20} color="#6B7280" />
      }
    />
  );
};

export default PrescriptionCard; 