import React, { useState, useLayoutEffect } from 'react';
import { View, Text, Pressable, Dimensions } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Constants } from '@/types/database.types';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import ControlledInput from '../ControlledInput';
import DatePicker from '../ui/DatePicker';
import RouterSelectInput, { RouterSelectOption } from '../common/RouterSelectInput';
import { Button } from '../ui/Button';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { Medicine, useMedicines } from '@/hooks/entities/useMedicines';
import { usePatients, Patient } from '@/hooks/entities/usePatients';
import { medicineFormSchema, MedicineFormData } from '@/schema/medicine';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import ImageCapture, { ImageCaptureResult } from '../common/ImageCapture';
import Animated, { interpolate, SlideInDown, useAnimatedRef, useAnimatedStyle, useScrollViewOffset } from 'react-native-reanimated';

export type InitialMedicineFormValues = Omit<Medicine, 'created_at' | 'updated_at' | 'user_id'> & {
  image_path?: string | null;
  image_bucket_id?: string | null;
};

interface MedicineFormProps {
  initialValues?: Partial<InitialMedicineFormValues>;
  onSubmit: (data: MedicineFormData, fileUri?: string, deleteFile?: boolean) => void;
  isLoading?: boolean;
  isEditing: boolean;
  medicineId?: string;
}

const MedicineForm: React.FC<MedicineFormProps> = ({
  initialValues,
  onSubmit,
  isLoading: isFormSubmissionLoading = false,
  isEditing,
  medicineId,
}) => {
  const navigation = useNavigation();
  const router = useRouter();
  const { confirm, showSuccess, showError } = useAppAlerts();
  const { useDeleteMedicine } = useMedicines();
  const deleteMutation = useDeleteMedicine();

  const [newLocalFileUri, setNewLocalFileUri] = useState<string | null>(null);
  const [deleteFile, setDeleteFile] = useState(false);

  const { control, handleSubmit, formState: { errors }, setValue, getValues } = useForm<MedicineFormData>({
    resolver: zodResolver(medicineFormSchema),
    defaultValues: initialValues ? {
      name: initialValues.name || '',
      description: initialValues.description || '',
      dosage_amount: initialValues.dosage_amount ?? undefined,
      dosage_unit: initialValues.dosage_unit ?? undefined,
      frequency_amount: initialValues.frequency_amount ?? undefined,
      frequency_unit: initialValues.frequency_unit ?? undefined,
      duration_amount: initialValues.duration_amount ?? undefined,
      duration_unit: initialValues.duration_unit ?? undefined,
      expiration_date: initialValues.expiration_date ?? undefined,
      opened_on_date: initialValues.opened_on_date ?? undefined,
      patient_id: initialValues.patient_id ?? undefined,
      prescription_id: initialValues.prescription_id ?? undefined,
      notes: initialValues.notes || '',
      price: initialValues.price ?? undefined,
      med_id: initialValues.med_id ?? undefined,
      is_custom: initialValues.is_custom === undefined 
                     ? (initialValues.med_id ? false : true)
                     : initialValues.is_custom,
    } : {
      name: '',
      description: '',
      dosage_amount: undefined,
      dosage_unit: undefined,
      frequency_amount: undefined,
      frequency_unit: undefined,
      duration_amount: undefined,
      duration_unit: undefined,
      expiration_date: undefined,
      opened_on_date: undefined,
      patient_id: undefined,
      prescription_id: undefined,
      notes: '',
      price: undefined,
      med_id: undefined,
      is_custom: true,
    },
  });

  const { useFetchPatients } = usePatients();
  const patientsQuery = useFetchPatients();
  
  const patientOptions = React.useMemo(() => {
    if (!patientsQuery.data) return [];
    return patientsQuery.data.map((patient: Patient): RouterSelectOption => ({
      id: patient.id,
      label: patient.name,
      subtitle: patient.bloodtype ? `Blood Type: ${patient.bloodtype}` : undefined,
      itemType: 'patient',
      data: patient,
    }));
  }, [patientsQuery.data]);

  const dosageUnitRouterOptions = Constants.public.Enums.dosage_unit_enum.map(unit => ({ id: unit, label: unit, subtitle: undefined }) as RouterSelectOption);
  const frequencyUnitRouterOptions = Constants.public.Enums.frequency_unit_enum.map(unit => ({ id: unit, label: unit, subtitle: undefined }) as RouterSelectOption);
  const durationUnitRouterOptions = Constants.public.Enums.duration_unit_enum.map(unit => ({ id: unit, label: unit, subtitle: undefined }) as RouterSelectOption);

  const { 
    url: initialAttachmentUrl, 
    isLoading: isLoadingInitialUrl, 
    error: initialUrlError 
  } = useSupabaseStorageUrl({
    bucketId: initialValues?.image_bucket_id || 'user-meds',
    path: initialValues?.image_path,
  });


  const handleImageCapturedForForm = (result: ImageCaptureResult) => {
    setNewLocalFileUri(result.uri);
    setDeleteFile(false);
    
  };

  const handleImageRemovedForForm = () => {
    setNewLocalFileUri(null);
    if (initialValues?.image_path) {
      setDeleteFile(true);
    }
    
  };

  const handleImageErrorForForm = (error: Error) => {
    showError(error.message, 'Image Error');
  };

  const onFormSubmit = (data: MedicineFormData) => {
    onSubmit(data, newLocalFileUri || undefined, deleteFile);
  };

  const handleDeletePress = () => {
    if (!medicineId) return;
    confirm({
      title: 'Confirm Deletion',
      message: 'Are you sure you want to delete this medicine? This action cannot be undone.',
      confirmText: 'Delete',
      style: 'destructive',
      onConfirm: async () => {
        try {
          await deleteMutation.mutateAsync({ id: medicineId });
          showSuccess('Medicine has been deleted.', 'Deleted');
          router.dismiss(); 
        } catch (error: any) {
          console.error('Delete Error from Form:', error);
          showError(error.message || 'Failed to delete medicine.');
        }
      },
    });
  };
  const IMG_HEIGHT = 300;
  const width = Dimensions.get('window').width;
  const scrollRef = useAnimatedRef<Animated.ScrollView>();

  const scrolOffset = useScrollViewOffset(scrollRef);
  const imageAnimatedStyle = useAnimatedStyle(() => {
    return {  
      transform: [{ 
        translateY: interpolate(scrolOffset.value, 
          [-IMG_HEIGHT, 0, IMG_HEIGHT],
          [IMG_HEIGHT/2, 0, IMG_HEIGHT*0.75]
          
          ,)
      },
      {
        scale: interpolate(scrolOffset.value, 
        [-IMG_HEIGHT, 0, IMG_HEIGHT],
        [2, 1, 2]
      ),
      }
    ],
    };
  });

  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditing ? 'Edit Medicine' : 'Add Medicine',
      headerRight: () => (
        isEditing && medicineId && (
          <Pressable onPress={handleDeletePress} disabled={deleteMutation.isPending || isFormSubmissionLoading} style={{ marginRight: 15 }}>
            <Ionicons
              name="trash"
              size={24}
              color={(deleteMutation.isPending || isFormSubmissionLoading) ? 'gray' : 'red'} 
              
            />
          </Pressable>
        )
      ),
    });
  }, [navigation, isEditing, medicineId, deleteMutation.isPending, isFormSubmissionLoading, confirm, router, showError, showSuccess, handleDeletePress]);

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900" >
    <Animated.ScrollView 
        ref={scrollRef}
        className="flex-1  bg-background dark:bg-neutral-900"
        contentContainerStyle={{ paddingBottom: 100 }}
        keyboardShouldPersistTaps="handled"
        scrollEventThrottle={16}
      >
        {/* <View className="mb-4 w-full"> */}
            <ImageCapture
                initialImage={initialAttachmentUrl}
                onImageCaptured={handleImageCapturedForForm}
                onRemove={handleImageRemovedForForm}
                onError={handleImageErrorForForm}
                enableOcr={false}
                placeholderText="Tap to add medicine image"
                placeholderImageSource={require('@/assets/images/icon.png')}
                imageHeight={IMG_HEIGHT}
                imageWidth={width}
                animatedImageStyle={imageAnimatedStyle}
            />
            
            {isLoadingInitialUrl && <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1 text-center">Loading initial image...</Text>}
            {initialUrlError && !newLocalFileUri && initialValues?.image_path && (
                <Text className="text-sm text-destructive dark:text-red-400 mt-1 text-center">
                    Failed to load initial images. Check connection or try re-selecting.
                </Text>
            )}
        {/* </View> */}
        <View className="flex-1 px-4 bg-background dark:bg-neutral-900 py-4">
        {/* Rest of the form inputs */}
        <ControlledInput
          control={control}
          name="name"
          label="Medicine Name"
          placeholder="e.g., Paracetamol 500mg"scrollEnabled
          leftIcon="medkit"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="price"
          label="Price (Optional)"
          placeholder="e.g., 10.99"
          keyboardType="numeric"
          leftIcon="cash-outline"
        />
        <ControlledInput
          control={control}
          name="description"
          label="Description (Optional)"
          placeholder="e.g., For headache relief"
          leftIcon="document-text-outline"
          multiline
        />

        <View className="flex-row space-x-2">
          <View className="flex-1">
            <ControlledInput
              control={control}
              name="dosage_amount"
              label="Dosage Amount"
              placeholder="e.g., 500"
              keyboardType="numeric"
            />
          </View>
          <View className="flex-1">
              <Controller
                control={control}
                name="dosage_unit"
                render={({ field: { onChange, value } }) => (
                  <RouterSelectInput
                    label="Unit"
                    options={dosageUnitRouterOptions}
                    selectedOption={dosageUnitRouterOptions.find(option => option.id === value)}
                    onSelect={(option) => onChange(option.id || undefined)}
                    modalTitle="Select Dosage Unit"
                    placeholder="Select unit"
                    error={errors.dosage_unit?.message}
                    clearable={true}
                  />
                )}
              />
          </View>
        </View>

        <View className="flex-row space-x-2">
           <View className="flex-1">
            <ControlledInput
              control={control}
              name="frequency_amount"
              label="Frequency Amount"
              placeholder="e.g., 3"
              keyboardType="numeric"
            />
          </View>
          <View className="flex-1">
             <Controller
                control={control}
                name="frequency_unit"
                render={({ field: { onChange, value } }) => (
                  <RouterSelectInput
                    label="Frequency"
                    options={frequencyUnitRouterOptions}
                    selectedOption={frequencyUnitRouterOptions.find(option => option.id === value)}
                    onSelect={(option) => onChange(option.id || undefined)}
                    modalTitle="Select Frequency Unit"
                    placeholder="Select frequency"
                    error={errors.frequency_unit?.message}
                    clearable={true}
                    showSearchInModal={false}
                  />
                )}
              />
          </View>
        </View>

        <View className="flex-row space-x-2 gap-4">
          <View className="flex-1">
            <ControlledInput
              control={control}
              name="duration_amount"
              label="Duration Amount"
              placeholder="e.g., 7"
              keyboardType="numeric"
            />
          </View>
          <View className="flex-1">
              <Controller
                control={control}
                name="duration_unit"
                render={({ field: { onChange, value } }) => (
                  <RouterSelectInput
                    label="Duration"
                    options={durationUnitRouterOptions}
                    selectedOption={durationUnitRouterOptions.find(option => option.id === value)}
                    onSelect={(option) => onChange(option.id || undefined)}
                    modalTitle="Select Duration Unit"
                    placeholder="Select duration"
                    error={errors.duration_unit?.message}
                    clearable={true}
                    showSearchInModal={false}
                  />
                )}
              />
          </View>
        </View>

        <View className="mb-4">
          <Controller
            control={control}
            name="patient_id" 
            render={({ field: { onChange, value } }) => (
              <RouterSelectInput
                label="Assign to Patient (Optional)"
                options={patientOptions}
                selectedOption={patientOptions.find(option => option.id === value)}
                onSelect={(option) => onChange(option?.id ?? null)}
                isLoading={patientsQuery.isLoading}
                modalTitle="Select Patient"
                placeholder="Select a patient (optional)"
                error={errors.patient_id?.message}
                showCreateNewButton={true} 
                onCreateNew={() => router.push('/modals/modal-patient')} 
                createText="Create New Patient"
                modalPlaceholder="Search patients..."
                modalEmptyResultsMessage={
                  patientsQuery.isError
                    ? 'Error loading patients'
                    : 'No patients found. You can create one from the Patients screen.'
                }
                clearable={true} 
              />
            )}
          />
        </View>
        <View className="flex-row space-x-2 gap-4">
         <View className="flex-1">
          <Controller
            control={control}
            name="expiration_date"
            render={({ field: { onChange, value } }) => (
              <DatePicker
                label="Expiration Date (Optional)"
                value={value ? new Date(value) : undefined}
                onChange={(date) => onChange(date ? date.toISOString() : null)} 
                placeholder="Select expiration date"
              />
            )}
          />
        </View>

         <View className="flex-1">
          <Controller
            control={control}
            name="opened_on_date"
            render={({ field: { onChange, value } }) => (
              <DatePicker
                label="Opened On Date (Optional)"
                value={value ? new Date(value) : undefined}
                onChange={(date) => onChange(date ? date.toISOString() : null)} 
                placeholder="Select date opened"
                maximumDate={new Date()} 
              />
            )}
          />
        </View>
        </View>
        <View className="flex-1 bg-background dark:bg-neutral-900">
        <ControlledInput
          control={control}
          name="notes"
          label="Notes (Optional)"
          placeholder="e.g., Take with food"
          leftIcon="reader-outline"
          multiline
        />
      </View>
       </View>
      </Animated.ScrollView>
      <Animated.View className="absolute bottom-5 left-4 right-4 h-20 py-3 px-4" entering={SlideInDown.delay(300)}>
        <Button
          title={initialValues ? 'Save Changes' : 'Add Medicine'}
          onPress={handleSubmit(onFormSubmit)}
          variant="primary"
          isLoading={isFormSubmissionLoading}
          icon={initialValues ? "save" : "add-circle"}
          fullWidth={false}
        />
        </Animated.View>
    </View>
  );
};

export default MedicineForm; 