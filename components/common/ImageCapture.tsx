import React, { useEffect } from 'react';
import { View, Alert, Pressable } from 'react-native';
import { ImageContentFit, ImageSource } from 'expo-image';
import ImageViewing from 'react-native-image-viewing';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withSpring,
  AnimateStyle 
} from 'react-native-reanimated';

import { 
  useImageCapture, 
  UseImageCaptureProps, 
  ImageCaptureResult as HookImageCaptureResult 
} from '@/hooks/useImageCapture';
import ImagePreview from './ImageCaptureUI/ImagePreview';
import FullScreenStatusIndicator from './FullScreenStatusIndicator';

// Re-exporting for consumers, or define a new one if it needs to differ.
// For now, let's assume it's the same as the hook's result for simplicity.
export type ImageCaptureResult = HookImageCaptureResult;

// Props for the refactored ImageCapture component
// These should largely match the original ImageCaptureProps for compatibility
interface ImageCaptureProps {
  onImageCaptured: (result: ImageCaptureResult) => void;
  onError?: (error: Error) => void;
  onRemove?: () => void; // This will be mapped to the hook's onImageRemoved
  initialImage?: string | null;
  enableOcr?: boolean;
  imageHeight?: number;
  imageWidth?: string | number;
  contentFit?: ImageContentFit;
  aspectRatio?: number;
  containerClassName?: string;
  placeholderText?: string;
  placeholderImageSource?: ImageSource | number;
  animatedImageStyle?: AnimateStyle<any>; // Added prop for override
  // OCR is a hook concern, not directly a UI concern for this component wrapper
  // manipulationOptions & pickerOptions can be passed to useImageCapture if further customization is needed by a parent
  // For this wrapper, we use sensible defaults in the hook call.
}

const ImageCapture: React.FC<ImageCaptureProps> = ({
  onImageCaptured,
  onError,
  onRemove,
  initialImage,
  enableOcr = true, // Default enableOcr, can be overridden
  imageHeight,
  imageWidth,
  contentFit,
  aspectRatio,
  containerClassName,
  placeholderText = "Tap to add image", // More action-oriented placeholder
  placeholderImageSource,
  animatedImageStyle: customAnimatedImageStyle, // Renamed for clarity if provided by parent
}) => {

  const hookProps: UseImageCaptureProps = {
    onImageCaptured,
    onError,
    onImageRemoved: onRemove,
    initialImage,
    enableOcr,
    // Sensible defaults for manipulation and picker options for this component
    manipulationOptions: { resizeWidth: 1024, compressQuality: 0.7 },
    pickerOptions: { allowsEditing: true, quality: 0.8 },
  };

  const {
    imageUri,
    isLoading: isGloballyLoading,
    error: globalError,
    previewLoadError,
    setPreviewLoadError,
    isViewerVisible,
    actions,
  } = useImageCapture(hookProps);

  // --- Default Animation Logic --- 
  const defaultImageOpacity = useSharedValue(0);
  const defaultImageScale = useSharedValue(0.95);

  const defaultAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: defaultImageOpacity.value,
      transform: [{ scale: defaultImageScale.value }],
    };
  });

  useEffect(() => {
    if (imageUri) {
      defaultImageOpacity.value = withTiming(1, { duration: 400 });
      defaultImageScale.value = withSpring(1, { damping: 15, stiffness: 100 });
    } else {
      // Reset instantly if no image, or if image is removed
      defaultImageOpacity.value = 0;
      defaultImageScale.value = 0.95;
    }
  }, [imageUri]); // Depend on imageUri from the hook
  // --- End Default Animation Logic ---

  const finalAnimatedImageStyle = customAnimatedImageStyle || defaultAnimatedStyle;

  const showImageSourceOptions = () => {
    Alert.alert(
      "Choose Image Source",
      "Select a source for your image.",
      [
        {
          text: "Take Photo",
          onPress: async () => {
            try {
              await actions.takePhoto();
            } catch (e) { /* Error handled by hook's onError */ }
          },
        },
        {
          text: "Choose from Library",
          onPress: async () => {
            try {
              await actions.chooseFromLibrary();
            } catch (e) { /* Error handled by hook's onError */ }
          },
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ],
      { cancelable: true }
    );
  };

  const handleImagePreviewLoadError = () => {
    actions.clearError(); 
    setPreviewLoadError(true);
    // Propagate this specific error if the main onError prop is provided
    onError?.(new Error('Failed to load image preview. The image might be corrupt or inaccessible.')); 
  };

  const loadingMessage = isGloballyLoading ? (enableOcr && imageUri ? "Processing with OCR..." : "Processing image...") : undefined;

  return (
    <View className={containerClassName}>
      <FullScreenStatusIndicator 
        isLoading={isGloballyLoading} 
        error={globalError} 
        loadingMessage={loadingMessage}
        onRetry={globalError ? actions.clearError : undefined}
      />

      {/* Render ImagePreview: either the image, or a pressable placeholder */}
      {/* We don't show this part if a global error is already displayed from the hook, or if globally loading */}
      {!isGloballyLoading && !globalError && (
        <Pressable onPress={!imageUri ? showImageSourceOptions : undefined} disabled={!!imageUri || isGloballyLoading}>
          <ImagePreview
            uri={imageUri} // This will be null for placeholder, or the image URI
            // For placeholder state (imageUri is null):
            // - onPressPreview is not set, the outer Pressable handles it.
            // - onRemoveImage/onChangeImage are irrelevant.
            // For image present state (imageUri is not null):
            // - The outer Pressable is disabled.
            // - onPressPreview opens the viewer.
            // - onRemoveImage calls hook's remove action.
            // - onChangeImage shows source options.
            onPressPreview={imageUri ? actions.openImageViewer : undefined}
            onRemoveImage={imageUri && onRemove ? actions.removeImage : undefined}
            onChangeImage={imageUri ? showImageSourceOptions : undefined}
            showRemoveButton={!!(imageUri && onRemove)}
            showChangeButton={!!imageUri}
            
            imageHeight={imageHeight}
            imageWidth={imageWidth}
            contentFit={contentFit}
            aspectRatio={aspectRatio}
            placeholderText={placeholderText}
            placeholderImageSource={placeholderImageSource}
            previewLoadError={previewLoadError}
            onImageLoadError={handleImagePreviewLoadError}
            onErrorRetry={previewLoadError ? () => setPreviewLoadError(false) : undefined}
            animatedImageStyle={finalAnimatedImageStyle} // Pass the determined style
          />
        </Pressable>
      )}

      {imageUri && (
        <ImageViewing
          images={[{ uri: imageUri }]}
          imageIndex={0}
          visible={isViewerVisible}
          onRequestClose={actions.closeImageViewer}
        />
      )}
    </View>
  );
};

export default ImageCapture; 