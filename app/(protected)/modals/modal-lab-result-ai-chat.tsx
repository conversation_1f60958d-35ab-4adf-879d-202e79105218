import React, { useState, useEffect, useRef } from 'react';
import { View, Text, FlatList, KeyboardAvoidingView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router, Stack } from 'expo-router';
import { useLabResultAI, type ChatMessage } from '@/hooks/useLabResultAI';
import ChatMessageComponent from '@/components/chat/ChatMessage';
import ChatInput from '@/components/chat/ChatInput';
import SuggestedQuestions from '@/components/chat/SuggestedQuestions';
import FullScreenStatusIndicator from '@/components/common/FullScreenStatusIndicator';
import { useAppAlerts } from '@/hooks/useAppAlerts';

export default function LabResultAIChatModal() {
  const params = useLocalSearchParams<{ id: string }>();
  const labResultId = params.id;
  const { showError } = useAppAlerts();
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [analysis, setAnalysis] = useState<any>(null);
  
  const flatListRef = useRef<FlatList>(null);

  const { 
    analyzeLabResult, 
    sendChatMessage, 
    useFetchAnalysis, 
    useFetchChatHistory 
  } = useLabResultAI();

  const { 
    data: existingAnalysis, 
    isLoading: isLoadingAnalysis, 
    error: analysisError 
  } = useFetchAnalysis(labResultId);

  const { 
    data: chatHistory, 
    isLoading: isLoadingChatHistory, 
    error: chatHistoryError 
  } = useFetchChatHistory(labResultId);

  // Handle analysis loading
  useEffect(() => {
    if (existingAnalysis) {
      setAnalysis(existingAnalysis);
    } else if (!isLoadingAnalysis && !analysisError) {
      // No existing analysis, trigger new analysis
      handleAnalyze();
    }
  }, [existingAnalysis, isLoadingAnalysis, analysisError]);

  // Handle chat history loading
  useEffect(() => {
    if (chatHistory && chatHistory.length > 0) {
      setMessages(chatHistory);
      setShowSuggestions(false);
    } else if (existingAnalysis && (!chatHistory || chatHistory.length === 0)) {
      // No chat history but we have analysis - show initial message
      const initialMessage: ChatMessage = {
        id: 'initial',
        role: 'assistant',
        content: `I've analyzed your lab results. Here's what I found:\n\n${existingAnalysis.ai_analysis.summary}\n\nFeel free to ask me any questions about your results!`,
        timestamp: new Date(),
      };
      setMessages([initialMessage]);
      setShowSuggestions(true);
    }
  }, [chatHistory, existingAnalysis]);

  const handleAnalyze = async () => {
    try {
      const result = await analyzeLabResult(labResultId);
      setAnalysis(result);
    } catch (error) {
      console.error('Analysis error:', error);
      showError('Failed to analyze lab results. Please try again.');
    }
  };

  const handleSendMessage = async (userMessage: string) => {
    if (!analysis) {
      showError('Lab results must be analyzed first');
      return;
    }

    // Add user message immediately
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: userMessage,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, newUserMessage]);
    setShowSuggestions(false);

    const aiMessageId = `ai-${Date.now()}`;
    
    try {
      setIsStreaming(true);
      
      // Add placeholder message for AI response
      const placeholderMessage: ChatMessage = {
        id: aiMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, placeholderMessage]);

      // Get response from sendChatMessage
      const response = await sendChatMessage(labResultId, userMessage, messages);
      
      // Parse the response content
      const responseText = await response.text();
      const data = JSON.parse(responseText);
      
      const assistantMessage = data.content || '';
      
      if (!assistantMessage) {
        throw new Error('No content received from AI');
      }

      // Update the placeholder message with the actual content
      setMessages(prev => prev.map(msg =>
        msg.id === aiMessageId ? { ...msg, content: assistantMessage } : msg
      ));
      
    } catch (error) {
      console.error('Chat error:', error);
      // Remove the placeholder message on error
      setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
      showError('Failed to send message. Please try again.');
    } finally {
      setIsStreaming(false);
    }
  };

  const handleQuestionPress = (question: string) => {
    handleSendMessage(question);
  };

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Loading state
  if (isLoadingAnalysis || isLoadingChatHistory) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <Stack.Screen options={{ title: 'AI Lab Analysis' }} />
        <FullScreenStatusIndicator 
          isLoading={true} 
          loadingMessage="Loading lab analysis..." 
        />
      </SafeAreaView>
    );
  }

  // Error state
  if (analysisError || chatHistoryError) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <Stack.Screen options={{ title: 'AI Lab Analysis' }} />
        <FullScreenStatusIndicator 
          error={analysisError || chatHistoryError} 
          errorMessage="Failed to load lab analysis data." 
          onRetry={() => {
            if (analysisError) handleAnalyze();
            // Chat history will auto-retry via React Query
          }}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
      <Stack.Screen options={{ title: 'AI Lab Analysis' }} />
      
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ChatMessageComponent message={item} />
          )}
          className="flex-1 px-4"
          contentContainerStyle={{ paddingVertical: 16 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        />

        {isStreaming && (
          <View className="px-4 py-2 border-t border-border dark:border-neutral-700 bg-muted/30 dark:bg-neutral-800/30">
            <View className="flex-row items-center">
              <Text className="text-sm text-muted-foreground dark:text-neutral-400">
                AI is typing...
              </Text>
            </View>
          </View>
        )}

        {showSuggestions && messages.length <= 1 && (
          <SuggestedQuestions
            onQuestionPress={handleQuestionPress}
            disabled={isStreaming}
          />
        )}

        <ChatInput
          onSendMessage={handleSendMessage}
          disabled={isStreaming}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
