import React from 'react';
import { Drawer } from 'expo-router/drawer';

const DrawerLayout = () => {
  return (
    <Drawer
      screenOptions={{
        drawerStyle: {
          width: 300,
        },
        headerShown: true,
      }}
    >
      <Drawer.Screen
        name="lab-result-ai-chat"
        options={{
          title: 'AI Lab Analysis',
        }}
      />
    </Drawer>
  );
};

export default DrawerLayout;