import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Card from '../ui/Card';
import { Patient } from '@/hooks/entities/usePatients'; // Assuming Patient type is exported from usePatients hook

interface PatientCardProps {
  patient: Patient;
  onPress?: () => void;
}

/**
 * A card component for displaying patient information in a list.
 */
const PatientCard: React.FC<PatientCardProps> = ({ patient, onPress }) => {
  const content = (
    <View className="space-y-1">
      {/* Age */}
      {patient.age !== null && patient.age !== undefined && (
        <View className="flex-row items-center">
          <Ionicons name="calendar-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-600 ml-1">Age: {patient.age}</Text>
        </View>
      )}
      {/* Blood Type */}
      {patient.bloodtype && (
        <View className="flex-row items-center">
          <Ionicons name="water-outline" size={16} color="#EF4444" />
          <Text className="text-sm text-gray-600 ml-1">Blood Type: {patient.bloodtype}</Text>
        </View>
      )}
    </View>
  );

  return (
    <Card
      title={patient.name}
      content={content}
      onPress={onPress}
      rightAction={
        <Ionicons name="chevron-forward" size={20} color="#6B7280" />
      }
    />
  );
};

export default PatientCard; 