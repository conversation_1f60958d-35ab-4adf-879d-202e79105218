import {router, Tabs, useSegments } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { TouchableOpacity, View } from "react-native";
import { Image } from "expo-image";
import { useUser } from '@clerk/clerk-expo';
import { useTheme } from '@react-navigation/native';
import { SearchHeader } from "@/components/common/SearchHeader";
import React, { useState } from 'react';

const TabsLayout = () => {
  const { user } = useUser();
  const segments = useSegments();
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  let modalRoute = 'modals/modal-prescription';
  const lastSegment = segments.length > 0 ? segments[segments.length - 1] : 'index';

  switch (lastSegment) {
    case 'index':
      modalRoute = 'modals/modal-prescription';
      break;
    case 'lab-results':
      modalRoute = 'modals/modal-labresult';
      break;
    case '(doctors-tabs)':
      modalRoute = 'modals/modal-doctor';
      break;
    case '(meds-tabs)':
      modalRoute = 'modals/modal-medicine';
      break;
    case 'all-meds':
    case 'meds':
      modalRoute = 'modals/modal-medicine';
      break;
    case 'doctors':
    case 'all-doctors':
      modalRoute = 'modals/modal-doctor';
      break;
    case 'patients':
      modalRoute = 'modals/modal-patient';
      break;
  }

  return (
    <Tabs
      screenOptions={{
        tabBarShowLabel: false,
        headerShown: true,
        headerShadowVisible: false,
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.text,
        tabBarStyle: {
          backgroundColor: theme.colors.card,
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
        },
        headerStyle: { 
          backgroundColor: theme.colors.card,
        },
        headerTitle: () => (
          <SearchHeader
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            theme={theme}
            placeholder="Search..."
          />
        ),
        headerRight: () => (
          <View className="flex-row gap-4 px-4">
            <Ionicons name="filter-outline" size={24} color={theme.colors.text} onPress={() => router.push("/modals/modal-filter")} />
            <Ionicons name="add-circle-outline" size={24} color={theme.colors.text} onPress={() => router.push(modalRoute as any)} />
          </View>
        ),
        headerLeft: () => (
          <TouchableOpacity className="px-4" onPress={() => router.push("/settings")}>
            <Image source={{uri: user?.imageUrl}} style={{width: 32, height: 32, borderRadius: 16}} />
          </TouchableOpacity>
        ),
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="document-text-outline" color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="lab-results"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="flask-outline" color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="(meds-tabs)"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="medkit-outline" color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="notifications"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="calendar-outline" color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="(doctors-tabs)"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="people-outline" color={color} size={size} />
          ),
        }}
      />
    </Tabs>
  );
}

export default TabsLayout;
