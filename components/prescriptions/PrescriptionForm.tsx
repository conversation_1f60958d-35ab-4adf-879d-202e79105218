import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { View, Text, Pressable, Dimensions } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigation, useRouter } from 'expo-router';
import Carousel from 'react-native-reanimated-carousel';
import { Ionicons } from '@expo/vector-icons';
import { useModalCreationStore } from '@/store/modalCreationStore';
import Animated, {
  interpolate,
  SlideInDown,
  useAnimatedRef,
  useAnimatedStyle,
  useScrollViewOffset
} from 'react-native-reanimated';
import ControlledInput from '../ControlledInput';
import ControlledDatePicker from '../ControlledDatePicker';
import RouterSelectInput, { RouterSelectOption } from '../common/RouterSelectInput';
import { Button } from '../ui/Button';
import ImageCapture, { ImageCaptureResult } from '../common/ImageCapture';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { usePatients, Patient } from '@/hooks/entities/usePatients';
import { useUserDoctors, UserDoctorWithDetails } from '@/hooks/entities/useUserDoctors';
import { usePrescriptions } from '@/hooks/entities/usePrescriptions';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import {
  prescriptionInsertSchema,
  PrescriptionInsert,
  PrescriptionUpdate,
  Prescription,
} from '@/schema/prescription';

interface PrescriptionFormProps {
  initialValues?: Partial<Prescription>;
  onSubmit: (
    data: PrescriptionInsert | PrescriptionUpdate,
    frontImageUri?: string,
    backImageUri?: string,
    deleteFrontImage?: boolean,
    deleteBackImage?: boolean
  ) => void;
  isLoading?: boolean;
  isEditing: boolean;
  prescriptionId?: string;
}

export function PrescriptionForm({
    initialValues,
    onSubmit,
    isLoading = false,
    isEditing,
    prescriptionId
}: PrescriptionFormProps) {

  const navigation = useNavigation();
  const router = useRouter();
  const { confirm, showSuccess, showError } = useAppAlerts();
  const { useDeletePrescription } = usePrescriptions();
  const deletePrescriptionMutation = useDeletePrescription();

  const {
    newlyCreatedPatientId,
    resetPatientCreation,
    newlyCreatedDoctorId,
    resetDoctorCreation
  } = useModalCreationStore();

  const [currentFrontImageUri, setCurrentFrontImageUri] = useState<string | null>(null);
  const [newLocalFrontImageUri, setNewLocalFrontImageUri] = useState<string | null>(null);
  const [deleteFrontImage, setDeleteFrontImage] = useState(false);

  const [currentBackImageUri, setCurrentBackImageUri] = useState<string | null>(null);
  const [newLocalBackImageUri, setNewLocalBackImageUri] = useState<string | null>(null);
  const [deleteBackImage, setDeleteBackImage] = useState(false);

  const { control, handleSubmit, reset, setValue, formState: { errors } } = useForm<PrescriptionInsert>({
    resolver: zodResolver(prescriptionInsertSchema),
    defaultValues: (initialValues as PrescriptionInsert) || {
      name: '',
      prescription_date: new Date().toISOString(),
      notes: '',
      patient_id: null,
      doctor_id: null,
    },
  });

  const { useFetchPatients } = usePatients();
  const { data: patientsData, isLoading: isLoadingPatients, refetch: refetchPatients } = useFetchPatients();

  const { useFetchUserDoctors } = useUserDoctors();
  const { data: doctorsData, isLoading: isLoadingDoctors, refetch: refetchDoctors } = useFetchUserDoctors();

  const patientOptions = React.useMemo((): RouterSelectOption[] => {
    return patientsData?.map((p: Patient): RouterSelectOption => ({
      id: p.id,
      label: p.name,
      subtitle: `ID: ${p.id.substring(0,8)}...`,
      itemType: 'patient',
      data: p,
    })) || [];
  }, [patientsData]);

  const doctorOptions = React.useMemo((): RouterSelectOption[] => {
    return doctorsData?.map((d: UserDoctorWithDetails): RouterSelectOption => ({
      id: d.id,
      label: d.name || 'Unnamed Doctor',
      subtitle: d.specialty || 'No specialty',
      itemType: 'doctor',
      data: d,
    })) || [];
  }, [doctorsData]);

  const {
    url: initialFrontUrl,
    isLoading: isLoadingFrontUrl,
    error: frontUrlError
  } = useSupabaseStorageUrl({
    bucketId: initialValues?.image_bucket_id,
    path: initialValues?.front_image_path
  });

  const {
    url: initialBackUrl,
    isLoading: isLoadingBackUrl,
    error: backUrlError
  } = useSupabaseStorageUrl({
    bucketId: initialValues?.image_bucket_id,
    path: initialValues?.back_image_path
  });

  const prevInitialValuesIdRef = useRef<string | null | undefined>(null);

  useEffect(() => {
    const currentEntityId = initialValues?.id;

    if (currentEntityId !== prevInitialValuesIdRef.current) {
      if (initialValues) {
        const processedInitialValues = {
          ...initialValues,
          prescription_date: initialValues.prescription_date
            ? new Date(initialValues.prescription_date).toISOString()
            : new Date().toISOString(),
        };
        reset(processedInitialValues as PrescriptionInsert);
      } else {
        reset({
          name: '',
          prescription_date: new Date().toISOString(),
          notes: '',
          patient_id: null,
          doctor_id: null,
        });
      }
    }
    prevInitialValuesIdRef.current = currentEntityId;
  }, [initialValues, reset]);

  useEffect(() => {
    setCurrentFrontImageUri(initialValues && initialFrontUrl ? initialFrontUrl : null);
    setCurrentBackImageUri(initialValues && initialBackUrl ? initialBackUrl : null);

    setNewLocalFrontImageUri(null);
    setNewLocalBackImageUri(null);
    setDeleteFrontImage(false);
    setDeleteBackImage(false);
  }, [initialValues, initialFrontUrl, initialBackUrl]);

  useEffect(() => {
    if (newlyCreatedPatientId) {
      if (patientsData && patientsData.some(p => p.id === newlyCreatedPatientId)) {
        setValue("patient_id", newlyCreatedPatientId);
        resetPatientCreation();
      } else if (!isLoadingPatients) {
        refetchPatients()
          .then(refetchResult => {
            if (refetchResult.data && refetchResult.data.some(p => p.id === newlyCreatedPatientId)) {
              setValue("patient_id", newlyCreatedPatientId);
            }
            resetPatientCreation();
          })
          .catch(error => {
            console.error("Error refetching patients in PrescriptionForm:", error);
            resetPatientCreation();
          });
      }
    }
  }, [
    newlyCreatedPatientId,
    patientsData,
    setValue,
    resetPatientCreation,
    refetchPatients,
    isLoadingPatients,
  ]);

  useEffect(() => {
    if (newlyCreatedDoctorId) {
      refetchDoctors().then(() => {
        setValue('doctor_id', newlyCreatedDoctorId, { shouldValidate: true });
        resetDoctorCreation();
      });
    }
  }, [
    newlyCreatedDoctorId,
    setValue,
    resetDoctorCreation,
    refetchDoctors,
  ]);

  const handleImageCaptured = (result: ImageCaptureResult | null, side: 'front' | 'back') => {
    if (side === 'front') {
      if (result?.uri) {
        setNewLocalFrontImageUri(result.uri);
        setCurrentFrontImageUri(result.uri);
        setDeleteFrontImage(false);
      } else {
        if (currentFrontImageUri || newLocalFrontImageUri) {
          setDeleteFrontImage(true);
        }
        setNewLocalFrontImageUri(null);
        setCurrentFrontImageUri(null);
      }
    } else if (side === 'back') {
      if (result?.uri) {
        setNewLocalBackImageUri(result.uri);
        setCurrentBackImageUri(result.uri);
        setDeleteBackImage(false);
      } else {
        if (currentBackImageUri || newLocalBackImageUri) {
          setDeleteBackImage(true);
        }
        setNewLocalBackImageUri(null);
        setCurrentBackImageUri(null);
      }
    }
  };

  const onFormSubmit = (data: PrescriptionInsert) => {
    onSubmit(
        data,
        newLocalFrontImageUri || undefined,
        newLocalBackImageUri || undefined,
        deleteFrontImage,
        deleteBackImage
    );
  };

  const handleDeletePress = () => {
    if (!prescriptionId) return;
    confirm({
      title: 'Confirm Deletion',
      message: 'Are you sure you want to delete this prescription? This action cannot be undone.',
      confirmText: 'Delete',
      style: 'destructive',
      onConfirm: async () => {
        try {
          await deletePrescriptionMutation.mutateAsync({ id: prescriptionId });
          showSuccess('Prescription deleted.', 'Deleted');
          router.dismiss();
        } catch (error: any) {
          console.error('Delete Error from PrescriptionForm:', error);
          showError(error.message || 'Failed to delete prescription.');
        }
      },
    });
  };

  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditing ? 'Edit Prescription' : 'Add Prescription',
      headerRight: () => (
        isEditing && prescriptionId && (
          <Pressable onPress={handleDeletePress} disabled={deletePrescriptionMutation.isPending} style={{ marginRight: 15 }}>
            <Ionicons
              name="trash-outline"
              size={24}
              color={deletePrescriptionMutation.isPending ? 'gray' : 'red'}
            />
          </Pressable>
        )
      ),
    });
  }, [navigation, isEditing, prescriptionId, deletePrescriptionMutation.isPending, confirm, showError, showSuccess, router]);

  const windowWidth = Dimensions.get('window').width;
  const carouselItemWidth = windowWidth;
  const carouselHeight = 300;

  // Animation setup
  const IMG_HEIGHT = carouselHeight + 50; // Add some padding for the carousel
  const scrollRef = useAnimatedRef<Animated.ScrollView>();

  const scrollOffset = useScrollViewOffset(scrollRef);
  const imageAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{
        translateY: interpolate(scrollOffset.value,
          [-IMG_HEIGHT, 0, IMG_HEIGHT],
          [IMG_HEIGHT/2, 0, IMG_HEIGHT*0.75]
        )
      },
      {
        scale: interpolate(scrollOffset.value,
          [-IMG_HEIGHT, 0, IMG_HEIGHT],
          [1.2, 1, 1.1]
        ),
      }
    ],
    opacity: interpolate(scrollOffset.value,
      [-IMG_HEIGHT, 0, IMG_HEIGHT],
      [0.9, 1, 0.95]
    ),
    };
  });

  // Create individual animation styles for each carousel item
  const frontImageAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{
        scale: interpolate(scrollOffset.value,
          [-IMG_HEIGHT, 0, IMG_HEIGHT/2],
          [1.1, 1, 0.98]
        ),
      }]
    };
  });

  const backImageAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{
        scale: interpolate(scrollOffset.value,
          [-IMG_HEIGHT, 0, IMG_HEIGHT/2],
          [1.1, 1, 0.98]
        ),
      }]
    };
  });

  const carouselData = [
    {
      type: "front" as const,
      displayUri: newLocalFrontImageUri || currentFrontImageUri,
      currentUriForCapture: newLocalFrontImageUri || currentFrontImageUri || initialFrontUrl,
      animatedStyle: frontImageAnimatedStyle,
    },
    {
      type: "back" as const,
      displayUri: newLocalBackImageUri || currentBackImageUri,
      currentUriForCapture: newLocalBackImageUri || currentBackImageUri || initialBackUrl,
      animatedStyle: backImageAnimatedStyle,
    },
  ];

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <Animated.ScrollView
        ref={scrollRef}
        className="flex-1 bg-background dark:bg-neutral-900"
        contentContainerStyle={{ paddingBottom: 100 }}
        keyboardShouldPersistTaps="handled"
        scrollEventThrottle={16}
      >
        <Animated.View style={imageAnimatedStyle}>
          <Carousel
            loop={false}
            width={carouselItemWidth}
            height={carouselHeight}
            autoPlay={false}
            data={carouselData}
            scrollAnimationDuration={500}
            renderItem={({ item }) => (
              <View
                key={item.type}
                className="flex-1 justify-center items-center bg-background dark:bg-neutral-900 overflow-hidden"
                style={{ width: carouselItemWidth, height: carouselHeight }}
              >
                <Text
                    className="text-xs font-medium text-white bg-black/50 px-2 py-0.5 rounded-b-md absolute top-0 z-20 self-center"
                    style={{ elevation: 1 }}
                >
                  {item.type === "front" ? "Front Side" : "Back Side"}
                </Text>
                <ImageCapture
                  onImageCaptured={(result) => handleImageCaptured(result, item.type)}
                  onRemove={() => handleImageCaptured(null, item.type)}
                  initialImage={item.displayUri || undefined}
                  onError={(err) => showError(err.message, 'Image Error')}
                  placeholderText={item.type === "front" ? "Add Front Image" : "Add Back Image"}
                  imageWidth="100%"
                  imageHeight={carouselHeight}
                  contentFit="cover"
                  containerClassName="w-full"
                  enableOcr={false}
                  placeholderImageSource={require('@/assets/images/icon.png')}
                  animatedImageStyle={item.animatedStyle}
                />
              </View>
            )}
          />

          {(isLoadingFrontUrl || isLoadingBackUrl) &&
            <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1 text-center">Loading images...</Text>
          }

          {(frontUrlError || backUrlError) &&
            <Text className="text-sm text-destructive dark:text-red-400 mt-1 text-center">
              Failed to load one or more images. Check connection or try re-selecting.
            </Text>
          }
        </Animated.View>

        <View className="flex-1 px-4 bg-background dark:bg-neutral-900 py-4 mt-4">
          <ControlledInput
            control={control}
            name="name"
            label="Prescription Name *"
            placeholder="e.g., Monthly Checkup Rx"
            leftIcon='document-text-outline'
          />

          <View className="mb-4">
              <ControlledDatePicker
                control={control}
                name="prescription_date"
                label="Prescription Date *"
              />
          </View>

          <View className="mb-4">
              <Controller
                control={control}
                name="patient_id"
                render={({ field: { onChange, value } }) => (
                  <RouterSelectInput
                    label="Patient (Optional)"
                    options={patientOptions}
                    selectedOption={patientOptions.find(option => option.id === value)}
                    onSelect={(option) => onChange(option.id)}
                    isLoading={isLoadingPatients}
                    modalTitle="Select Patient O"
                    placeholder="Select patient"
                    error={errors.patient_id?.message}
                    showCreateNewButton={true}
                    onCreateNew={() => router.push('/modals/modal-patient')}
                    createText="Add New Patient"
                    modalPlaceholder="Search patients..."
                  />
                )}
              />
          </View>

          <View className="mb-4">
              
              <Controller
                control={control}
                name="doctor_id"
                render={({ field: { onChange, value } }) => (
                  <RouterSelectInput
                    label="Doctor (Optional)"
                    options={doctorOptions}
                    selectedOption={doctorOptions.find(option => option.id === value)}
                    onSelect={(option) => onChange(option.id)}
                    isLoading={isLoadingDoctors}
                    modalTitle="Select Doctor"
                    placeholder="Select doctor (optional)"
                    error={errors.doctor_id?.message}
                    showCreateNewButton={true}
                    onCreateNew={() => router.push('/modals/modal-doctor')}
                    createText="Add New Doctor"
                    modalPlaceholder="Search doctors..."
                    clearable={true}
                  />
                )}
              />
          </View>

          <ControlledInput
            control={control}
            name="notes"
            label="Notes (Optional)"
            placeholder="Any additional details..."
            multiline
            leftIcon='reader-outline'
            textAlignVertical="top"
          />
        </View>
      </Animated.ScrollView>

      <Animated.View className="absolute bottom-5 left-4 right-4 h-20 py-3 px-4" entering={SlideInDown.delay(300)}>
        <Button
          title={isEditing ? 'Update Prescription' : 'Add Prescription'}
          onPress={handleSubmit(onFormSubmit)}
          variant="primary"
          isLoading={isLoading}
          icon={isEditing ? "save" : "add-circle"}
          fullWidth={false}
        />
      </Animated.View>
    </View>
  );
}