import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { FlashList } from '@shopify/flash-list';
import { Ionicons } from '@expo/vector-icons';
import { useUserDoctors } from '@/hooks/entities/useUserDoctors';
import UserDoctorCard from '@/components/doctors/UserDoctorCard';
import { Button } from '@/components/ui/Button';

/**
 * Doctors screen showing a list of the user's associated doctors (within doctors-tabs)
 */
export default function DoctorsScreen() {
  const { useFetchUserDoctors } = useUserDoctors();
  const { data: doctors, isLoading, isError, refetch } = useFetchUserDoctors();

  // Navigate to the modal screen for viewing/editing
  const handlePressDoctor = (id: string) => {
    router.push({ pathname: '/modals/modal-doctor', params: { id } });
  };

  // Navigate to the modal screen for creation
  const handleCreate = () => {
    router.push('/modals/modal-doctor');
  };

  // Show loading state
  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">Loading doctors...</Text>
      </View>
    );
  }

  // Show error state
  if (isError) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="alert-circle" size={48} className="text-destructive dark:text-red-400" />
        <Text className="text-destructive dark:text-red-400 mt-4 mb-4 text-center">
          Failed to load doctors
        </Text>
        <Button title="Try Again" onPress={() => refetch()} variant="primary" />
      </View>
    );
  }

  // Show empty state
  if (!doctors || doctors.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="medkit-outline" size={48} className="text-muted-foreground dark:text-neutral-500" />
        <Text className="text-muted-foreground dark:text-neutral-400 mt-4 mb-2 text-center font-medium text-lg">
          No Doctors Added Yet
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-4 text-center px-6">
          Add doctors you visit to keep track of their details.
        </Text>
        <Button title="Add First Doctor" onPress={handleCreate} variant="primary" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
    
      <FlashList
        data={doctors}
        renderItem={({ item }) => (
          <UserDoctorCard
            doctor={item}
            onPress={() => handlePressDoctor(item.id)}
          />
        )}
        estimatedItemSize={120} // Adjust estimated size
        contentContainerClassName="p-4"
        ItemSeparatorComponent={() => <View className="h-3" />}
      />

      
    </View>
  );
}