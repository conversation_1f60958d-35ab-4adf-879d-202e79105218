import React from 'react';
import {
  View,
  Text,
  Pressable,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useRouterSelectStore } from '@/store/routerSelectStore';

// Define option type for the select
export interface RouterSelectOption {
  id: string;
  label: string;
  subtitle?: string; // Ensure subtitle is part of the interface
  itemType?: 'doctor' | 'patient' | 'generic'; // Added itemType
  data?: any; // Added data to hold the full object for card rendering
}

export interface RouterSelectInputProps {
  // Data and behavior props
  options: RouterSelectOption[];
  selectedOption?: RouterSelectOption; // The currently selected option object
  onSelect: (option: RouterSelectOption) => void; // Callback when an option is selected
  onSearch?: (query: string) => void; // Optional: for server-side search
  isLoading?: boolean; // To show loading state, typically managed by onSearch

  // Presentation props for the trigger
  label?: string;
  placeholder?: string;
  error?: string; // Error message to display below the input
  disabled?: boolean;
  leftIcon?: keyof typeof Ionicons.glyphMap;

  // Behavior for the modal
  modalTitle?: string;
  showCreateNewButton?: boolean;
  onCreateNew?: () => void; // Callback if 'Create New' is pressed
  createText?: string;
  modalPlaceholder?: string; // Placeholder for the search bar inside the modal
  modalEmptyResultsMessage?: string;
  showSearchInModal?: boolean; // New prop

  // Styling
  style?: StyleProp<ViewStyle>; // Style for the main container (for backward compatibility)
  inputStyle?: StyleProp<ViewStyle>; // Style for the pressable input area (for backward compatibility)
  labelStyle?: StyleProp<TextStyle>; // (for backward compatibility)
  textStyle?: StyleProp<TextStyle>; // Style for the selected text / placeholder text (for backward compatibility)
  errorStyle?: StyleProp<TextStyle>; // (for backward compatibility)
  clearable?: boolean;
}

const RouterSelectInput: React.FC<RouterSelectInputProps> = ({
  options,
  selectedOption,
  onSelect,
  onSearch,
  isLoading = false,
  label,
  placeholder = 'Select an option',
  error,
  disabled = false,
  leftIcon,
  modalTitle = 'Select Option',
  showCreateNewButton = false,
  onCreateNew,
  createText = 'Create New',
  modalPlaceholder,
  modalEmptyResultsMessage,
  style,
  inputStyle,
  labelStyle,
  textStyle,
  errorStyle,
  clearable = true,
  showSearchInModal = true, // Default to true
}) => {
  const router = useRouter();
  const { open, close: closeStoreModal } = useRouterSelectStore();

  const handleOpenModal = () => {
    if (disabled) return;

    open({
      title: modalTitle,
      options,
      selectedOption,
      onSelect: (optionFromModal) => {
        onSelect(optionFromModal);
      },
      onSearch,
      isLoading,
      showCreateNewButton,
      onCreateNew: () => {
        if (onCreateNew) {
          closeStoreModal();
          router.dismiss();
          onCreateNew();
        }
      },
      createText,
      placeholder: modalPlaceholder || placeholder,
      emptyResultsMessage: modalEmptyResultsMessage,
      showSearchInModal, // Pass the new prop
    });

    router.push('/(protected)/modals/router-select-screen');
  };

  const handleClear = (e: { stopPropagation: () => void; }) => {
    e.stopPropagation();
    onSelect({ id: '', label: '' } as RouterSelectOption);
  };

  const displayLabel = selectedOption?.label || placeholder;
  const hasValue = !!selectedOption?.label;

  return (
    <View className="mb-4" style={style}>
      {label && (
        <Text className="mb-1 text-sm font-medium text-foreground dark:text-neutral-300" style={labelStyle}>
          {label}
        </Text>
      )}
      <Pressable
        onPress={handleOpenModal}
        disabled={disabled}
        className={`
          w-full rounded-lg border px-3 h-12 flex-row items-center
          ${error
            ? 'border-destructive bg-destructive/10 dark:border-destructive dark:bg-destructive/20'
            : 'border-input bg-background dark:border-neutral-700 dark:bg-neutral-800'}
          ${disabled ? 'opacity-50 bg-muted dark:bg-neutral-800' : ''}
        `}
        style={inputStyle}
      >
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={20}
            className={`mr-2 ${error ? 'text-destructive' : 'text-muted-foreground dark:text-neutral-400'}`}
          />
        )}
        <Text
          className={`
            flex-1 text-base
            ${hasValue
              ? (error ? 'text-destructive' : 'text-foreground dark:text-neutral-50')
              : 'text-muted-foreground dark:text-neutral-400'}
          `}
          style={textStyle}
          numberOfLines={1}
        >
          {displayLabel}
        </Text>
        {hasValue && clearable && !disabled && (
          <Pressable onPress={handleClear} className="p-1 ml-2" hitSlop={10}>
            <Ionicons name="close-circle-outline" size={20} className="text-muted-foreground dark:text-neutral-400" />
          </Pressable>
        )}
        {!disabled && (
          <Ionicons
            name="chevron-down"
            size={20}
            className="ml-1 text-muted-foreground dark:text-neutral-400"
          />
        )}
      </Pressable>
      {error && (
        <Text className="mt-1 text-sm text-destructive dark:text-red-400" style={errorStyle}>
          {error}
        </Text>
      )}
    </View>
  );
};

export default RouterSelectInput;
