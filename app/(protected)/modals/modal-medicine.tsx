import React from 'react';
import { SafeAreaView } from 'react-native';
import {useLocalSearchParams, useRouter } from 'expo-router';

import { useMedicines, MedicineWithRelations } from '@/hooks/entities/useMedicines';
import MedicineForm, { InitialMedicineFormValues } from '@/components/medications/MedicineForm';
import { MedicineFormData, medicineFormSchema } from '@/schema/medicine';
import { DosageUnitEnum, DurationUnitEnum, FrequencyUnitEnum } from '@/hooks/entities/useAllMedicines';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import FullScreenStatusIndicator from '@/components/common/FullScreenStatusIndicator';

export default function MedicineModalScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{
    id?: string;
    initialMedId?: string;
    initialName?: string;
    initialDescription?: string;
    initialPrice?: string;
    initialDosageAmount?: string;
    initialDosageUnit?: DosageUnitEnum;
    initialFrequencyAmount?: string;
    initialFrequencyUnit?: FrequencyUnitEnum;
    initialDurationAmount?: string;
    initialDurationUnit?: DurationUnitEnum;
  }>();
  
  const medicineIdFromNav = params.id;
  const isCreatingFromTemplate = !!params.initialName;

  const isEditing = !!medicineIdFromNav && !isCreatingFromTemplate;
  const medicineIdForForm = isEditing ? medicineIdFromNav : undefined;

  const { showSuccess, showError } = useAppAlerts();

  const { 
    useFetchMedicine, 
    useCreateMedicine, 
    useUpdateMedicine 
  } = useMedicines();

  const { 
    data: existingMedicineData,
    isLoading: isLoadingData, 
    isError: isFetchError, 
    error: fetchError 
  } = useFetchMedicine(medicineIdForForm);

  const createMutation = useCreateMedicine();
  const updateMutation = useUpdateMedicine();

  const handleFormSubmit = async (data: MedicineFormData, fileUri?: string, deleteFile?: boolean) => {
    try {
      if (isEditing && medicineIdForForm) {
        await updateMutation.mutateAsync({ id: medicineIdForForm, data, fileUri, deleteFile });
        showSuccess('Medicine updated successfully.');
      } else {
        await createMutation.mutateAsync({ data, fileUri });
        showSuccess('Medicine added successfully.');
      }
      router.dismiss();
    } catch (error: any) {
      console.error('Submit Error:', error);
      showError(error.message || 'Failed to save medicine. Please try again.');
    }
  };

  let formInitialValues: Partial<InitialMedicineFormValues> | undefined = undefined;

  if (isCreatingFromTemplate) {
    formInitialValues = {
      name: params.initialName,
      description: params.initialDescription || null,
      price: params.initialPrice ? parseFloat(params.initialPrice) : undefined,
      dosage_amount: params.initialDosageAmount ? parseFloat(params.initialDosageAmount) : undefined,
      dosage_unit: params.initialDosageUnit || null,
      frequency_amount: params.initialFrequencyAmount ? parseFloat(params.initialFrequencyAmount) : undefined,
      frequency_unit: params.initialFrequencyUnit || null,
      duration_amount: params.initialDurationAmount ? parseFloat(params.initialDurationAmount) : undefined,
      duration_unit: params.initialDurationUnit || null,
      med_id: params.initialMedId || null,
      is_custom: !params.initialMedId,
      expiration_date: null,
      opened_on_date: null,
      patient_id: null,
      prescription_id: null,
      notes: null,
      image_path: null,
      image_bucket_id: null,
      
    };
  } else if (isEditing && existingMedicineData) {
    formInitialValues = {
      id: existingMedicineData.id,
      name: existingMedicineData.name,
      description: existingMedicineData.description,
      dosage_amount: existingMedicineData.dosage_amount,
      dosage_unit: existingMedicineData.dosage_unit,
      frequency_amount: existingMedicineData.frequency_amount,
      frequency_unit: existingMedicineData.frequency_unit,
      duration_amount: existingMedicineData.duration_amount,
      duration_unit: existingMedicineData.duration_unit,
      expiration_date: existingMedicineData.expiration_date,
      opened_on_date: existingMedicineData.opened_on_date,
      patient_id: existingMedicineData.patient_id,
      prescription_id: existingMedicineData.prescription_id,
      notes: existingMedicineData.notes,
      price: existingMedicineData.price,
      med_id: existingMedicineData.med_id,
      is_custom: existingMedicineData.is_custom,
      image_path: existingMedicineData.image_path,
      image_bucket_id: existingMedicineData.image_bucket_id,
      
    };
  }

  if (isEditing && isLoadingData) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator 
          isLoading={true} 
          loadingMessage="Loading medicine details..." 
        />
      </SafeAreaView>
    );
  }

  if (isEditing && isFetchError) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
        <FullScreenStatusIndicator
          error={fetchError}
          errorMessage={`Error loading medicine data: ${fetchError?.message || 'Unknown error'}`}
          onRetry={() => {
            showError("Retry functionality for fetch error needs specific implementation.");
          }} 
        />
      </SafeAreaView>
    );
  }

  return (
    <MedicineForm 
      initialValues={formInitialValues}
      onSubmit={handleFormSubmit}
      isLoading={createMutation.isPending || updateMutation.isPending}
      isEditing={isEditing}
      medicineId={medicineIdForForm}
    />
  );
} 