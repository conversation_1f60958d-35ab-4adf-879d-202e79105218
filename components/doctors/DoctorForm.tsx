import React, { useLayoutEffect } from 'react';
import { ScrollView, View, Pressable } from 'react-native';
import { useForm, Resolver, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import ControlledInput from '../ControlledInput';
import { Button } from '../ui/Button';
import { doctorFormSchema, DoctorFormData } from '@/schema/doctor';
import { UserDoctor, useUserDoctors } from '@/hooks/entities/useUserDoctors';
import WorkingHoursSelector from '../WorkingHoursSelector';
import { WorkingHours } from '@/schema/working-hours';
import { useAppAlerts } from '@/hooks/useAppAlerts';

interface DoctorFormProps {
  initialValues?: UserDoctor;
  onSubmit: (data: DoctorFormData) => void;
  isLoading?: boolean;
  isEditing: boolean;
  doctorId?: string;
}

const DoctorForm: React.FC<DoctorFormProps> = ({
  initialValues,
  onSubmit,
  isLoading = false,
  isEditing,
  doctorId,
}) => {
  const navigation = useNavigation();
  const router = useRouter();
  const { confirm, showSuccess, showError } = useAppAlerts();
  const { useDeleteUserDoctor } = useUserDoctors();
  const deleteDoctorMutation = useDeleteUserDoctor();

  const { control, handleSubmit, setValue, watch } = useForm<DoctorFormData>({
    resolver: zodResolver(doctorFormSchema) as Resolver<DoctorFormData>,
    defaultValues: initialValues ? {
      name: initialValues.name,
      specialty: initialValues.specialty ?? undefined,
      workplace: initialValues.workplace ?? undefined,
      workplace_address: initialValues.workplace_address ?? undefined,
      phone_number: initialValues.phone_number ?? undefined,
      city: initialValues.city ?? undefined,
      notes: initialValues.notes ?? undefined,
      fee: initialValues.fee ?? undefined,
      working_hours: (initialValues.working_hours as WorkingHours | null) ?? null,
      doctor_id: initialValues.doctor_id ?? undefined,
    } : {
      name: '',
      specialty: undefined,
      workplace: undefined,
      workplace_address: undefined,
      phone_number: undefined,
      city: undefined,
      notes: undefined,
      fee: undefined,
      working_hours: null,
      doctor_id: undefined,
    },
  });

  const handleDeletePress = () => {
    if (!doctorId) return;

    confirm({
      title: 'Confirm Deletion',
      message: 'Are you sure you want to delete this doctor? Related prescriptions might be affected.',
      confirmText: 'Delete',
      style: 'destructive',
      onConfirm: async () => {
        try {
          await deleteDoctorMutation.mutateAsync({ id: doctorId });
          showSuccess('Doctor has been deleted.', 'Deleted');
          router.dismiss(); 
        } catch (error: any) {
          console.error('Delete Error from DoctorForm:', error);
          showError(error.message || 'Failed to delete doctor.');
        }
      },
    });
  };

  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditing ? 'Edit Doctor' : 'Add Doctor',
      headerRight: () => (
        isEditing && doctorId && (
          <Pressable onPress={handleDeletePress} disabled={deleteDoctorMutation.isPending} style={{ marginRight: 15 }}>
            <Ionicons
              name="trash-outline"
              size={24}
              color={deleteDoctorMutation.isPending ? 'gray' : 'red'} 
            />
          </Pressable>
        )
      ),
    });
  }, [navigation, isEditing, doctorId, deleteDoctorMutation.isPending, confirm, showError, showSuccess, router]);

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <ScrollView 
        className="flex-1 px-4" 
        contentContainerStyle={{ paddingVertical: 24 }}
        keyboardShouldPersistTaps="handled"
      >
        <ControlledInput
          control={control}
          name="name"
          label="Doctor Name"
          placeholder="Enter doctor's name"
          leftIcon="person"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="specialty"
          label="Specialty (Optional)"
          placeholder="e.g., Cardiology, General Practitioner"
          leftIcon="medkit-outline"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="workplace"
          label="Workplace (Optional)"
          placeholder="e.g., Central Hospital, Private Clinic"
          leftIcon="business-outline"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="workplace_address"
          label="Address (Optional)"
          placeholder="Enter workplace address"
          leftIcon="location-outline"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="phone_number"
          label="Phone Number (Optional)"
          placeholder="Enter phone number"
          leftIcon="call-outline"
          keyboardType="phone-pad"
        />
        <ControlledInput
          control={control}
          name="city"
          label="City (Optional)"
          placeholder="Enter city"
          leftIcon="map-outline"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="fee"
          label="Fee (Optional)"
          placeholder="Enter consultation fee"
          leftIcon="cash-outline"
          keyboardType="decimal-pad"
        />
        <Controller
          control={control}
          name="working_hours"
          render={({ field: { onChange, value } }) => (
            <WorkingHoursSelector
              value={value as WorkingHours | null | undefined}
              onChange={onChange}
            />
          )}
        />
        <ControlledInput
          control={control}
          name="notes"
          label="Notes (Optional)"
          placeholder="Add any relevant notes"
          leftIcon="document-text-outline"
          multiline
          numberOfLines={3}
          style={{ height: 80, paddingTop: 8 }} 
        />
      </ScrollView>
      <View className="px-4 py-3 border-t border-border dark:border-neutral-700 bg-background dark:bg-neutral-900">
        <Button
          title={isEditing ? 'Update Doctor' : 'Add Doctor'}
          onPress={handleSubmit(onSubmit)}
          isLoading={isLoading}
          icon={isEditing ? "save" : "add-circle"}
          fullWidth
        />
      </View>
    </View>
  );
};

export default DoctorForm; 