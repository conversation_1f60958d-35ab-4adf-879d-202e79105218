import React from 'react';
import { View, Text } from 'react-native';
import { differenceInDays } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import Card from '../ui/Card'; // Assuming a generic Card component exists
import { MedicineWithRelations } from '@/hooks/entities/useMedicines';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';

interface MedicineCardProps {
  medicine: MedicineWithRelations;
  onPress?: () => void;
}

// Function to format dosage information
const formatDosage = (med: MedicineWithRelations): string | null => {
  let dosage = '';
  if (med.dosage_amount && med.dosage_unit) {
    dosage += `${med.dosage_amount} ${med.dosage_unit}`;
  }
  if (med.frequency_amount && med.frequency_unit) {
    dosage += dosage ? `, ${med.frequency_amount} ${med.frequency_unit}` : `${med.frequency_amount} ${med.frequency_unit}`; 
  }
  return dosage || null;
};

// Function to format duration
const formatDuration = (med: MedicineWithRelations): string | null => {
  if (med.duration_unit === 'Ongoing' || med.duration_unit === 'As needed') {
    return med.duration_unit;
  }
  if (med.duration_amount && med.duration_unit) {
    return `${med.duration_amount} ${med.duration_unit}`;
  }
  return null;
};

/**
 * A card component for displaying a medicine in a list.
 */
const MedicineCard: React.FC<MedicineCardProps> = ({ medicine, onPress }) => {
  const dosageText = formatDosage(medicine);
  const durationText = formatDuration(medicine);

  // Get image URL
  const { url: imageUrl, isLoading: isLoadingUrl } = useSupabaseStorageUrl({
    bucketId: medicine.image_bucket_id || 'user-meds',
    path: medicine.image_path,
  });

  // Calculate days until expiration
  const today = new Date();
  const expirationDate = medicine.expiration_date ? new Date(medicine.expiration_date) : null;
  const daysUntilExpiration = expirationDate ? differenceInDays(expirationDate, today) : null;
  let expirationInfo: { text: string; color: string } | null = null;

  if (daysUntilExpiration !== null) {
    if (daysUntilExpiration < 0) {
      expirationInfo = { text: `Expired ${Math.abs(daysUntilExpiration)} days ago`, color: 'text-red-500' };
    } else if (daysUntilExpiration <= 30) {
      expirationInfo = { text: `Expires in ${daysUntilExpiration} days`, color: 'text-yellow-600' };
    } else {
      // Optionally show if not expiring soon, or hide
      // expirationInfo = { text: `Expires on ${format(expirationDate!, 'MMM d, yyyy')}`, color: 'text-gray-500' };
    }
  }

  // Content for the card
  const content = (
    <View className="space-y-2">
      {/* Dosage and Duration */}
      {(dosageText || durationText) && (
        <View className="flex-row items-center space-x-3">
          {dosageText && (
            <View className="flex-row items-center">
              <Ionicons name="eyedrop-outline" size={16} color="#6B7280" />
              <Text className="text-sm text-gray-600 ml-1">{dosageText}</Text>
            </View>
          )}
          {durationText && (
            <View className="flex-row items-center">
              <Ionicons name="calendar-outline" size={16} color="#6B7280" />
              <Text className="text-sm text-gray-600 ml-1">{durationText}</Text>
            </View>
          )}
        </View>
      )}

      {/* Patient Info */}
      {medicine.patients && (
        <View className="flex-row items-center">
          <Ionicons name="person-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">For: {medicine.patients.name}</Text>
        </View>
      )}

      {/* Expiration Info */}
      {expirationInfo && (
        <View className="flex-row items-center">
          <Ionicons name="warning-outline" size={16} color={expirationInfo.color.includes('red') ? '#EF4444' : '#D97706'} />
          <Text className={`text-sm ml-1 ${expirationInfo.color}`}>{expirationInfo.text}</Text>
        </View>
      )}

      {/* Description Snippet (Optional) */}
      {medicine.description && (
        <Text className="text-sm text-gray-500 italic" numberOfLines={1}>
          {medicine.description}
        </Text>
      )}
    </View>
  );

  return (
    <Card
      title={medicine.name}
      content={content}
      onPress={onPress}
      imageUrl={imageUrl}
      imageLoading={isLoadingUrl}
      rightAction={
        <Ionicons name="chevron-forward" size={20} color="#6B7280" />
      }
    />
  );
};

export default MedicineCard; 