import React, { useState } from 'react';
import { View, TextInput, Pressable, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  disabled = false,
  placeholder = "Ask about your lab results..."
}) => {
  const [message, setMessage] = useState('');

  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      onSendMessage(trimmedMessage);
      setMessage('');
    }
  };

  const canSend = message.trim().length > 0 && !disabled;

  return (
    <View className="flex-row items-end p-4 border-t border-border dark:border-neutral-700 bg-background dark:bg-neutral-900">
      <View className="flex-1 mr-3">
        <TextInput
          value={message}
          onChangeText={setMessage}
          placeholder={placeholder}
          placeholderTextColor="#9CA3AF"
          multiline
          maxLength={500}
          editable={!disabled}
          onSubmitEditing={handleSend}
          blurOnSubmit={false}
          className={`
            min-h-[44px] max-h-[120px] px-4 py-3 
            border border-border dark:border-neutral-700 
            rounded-2xl 
            bg-card dark:bg-neutral-800 
            text-foreground dark:text-neutral-200
            ${disabled ? 'opacity-50' : ''}
          `}
          style={{
            textAlignVertical: 'top',
            fontSize: 16,
            lineHeight: 20,
          }}
        />
      </View>
      
      <Pressable
        onPress={handleSend}
        disabled={!canSend}
        className={`
          w-11 h-11 rounded-full items-center justify-center
          ${canSend 
            ? 'bg-primary active:bg-primary/90' 
            : 'bg-muted dark:bg-neutral-700'
          }
        `}
      >
        {disabled ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Ionicons 
            name="send" 
            size={20} 
            color={canSend ? '#fff' : '#9CA3AF'} 
          />
        )}
      </Pressable>
    </View>
  );
};

export default ChatInput;