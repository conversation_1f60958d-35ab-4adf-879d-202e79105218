/// <reference types="https://deno.land/x/deno/cli/types/dts/lib.deno.d.ts" />
import { serve, ServerRequest } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient, SupabaseClient } from 'npm:@supabase/supabase-js@2'
import { getDocumentProxy, extractText as extractTextFromPdfLib } from 'npm:unpdf'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AnalysisResult {
  summary: string;
  keyFindings: string[];
  recommendations: string[];
  riskFactors: string[];
  timestamp: string;
  model_used: string;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { labResultId } = await req.json()
    
    if (!labResultId) {
      return new Response(JSON.stringify({
        error: 'Lab result ID is required'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authenticated user from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({
        error: 'Authorization header is required'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Extract user ID from JWT token
    const token = authHeader.replace('Bearer ', '')
    let userId: string
    try {
      // Decode JWT to get user ID from 'sub' claim
      const payload = JSON.parse(atob(token.split('.')[1]))
      userId = payload.sub
      if (!userId) {
        return new Response(JSON.stringify({
          error: 'User ID not found in token'
        }), {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Invalid authorization token'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 1. Fetch lab result and verify ownership
    const { data: labResult, error: labError } = await supabase
      .from('labresults')
      .select('*')
      .eq('id', labResultId)
      .eq('user_id', userId) // Verify ownership
      .single()

    if (labError || !labResult) {
      return new Response(JSON.stringify({
        error: 'Lab result not found or access denied',
        details: labError?.message
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    if (!labResult.captured_pdf_path) {
      return new Response(JSON.stringify({
        error: 'No PDF found for analysis'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 2. Check if analysis already exists
    const { data: existingAnalysis } = await supabase
      .from('lab_result_analyses')
      .select('*')
      .eq('lab_result_id', labResultId)
      .single()

    if (existingAnalysis) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          analysis: existingAnalysis,
          cached: true 
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // 3. Download PDF from storage
    const { data: pdfData, error: downloadError } = await supabase.storage
      .from(labResult.captured_pdf_bucket_id || 'labresults')
      .download(labResult.captured_pdf_path)

    if (downloadError || !pdfData) {
      throw new Error(`Failed to download PDF for analysis: ${downloadError ? (downloadError as Error).message : 'Unknown reason'}`)
    }

    // 4. Extract text from PDF (simplified - in production use proper PDF parser)
    const pdfText = await extractTextFromPDF(pdfData)

    if (!pdfText || pdfText.length < 50) {
      throw new Error('Could not extract sufficient text from PDF')
    }

    // 5. Analyze with OpenAI
    const analysis = await analyzeLabResultWithAI(pdfText)

    // 6. Store analysis results
    const { data: analysisRecord, error: insertError } = await supabase
      .from('lab_result_analyses')
      .insert({
        lab_result_id: labResultId,
        extracted_text: pdfText.substring(0, 10000), // Limit text storage
        ai_analysis: analysis,
      })
      .select()
      .single()

    if (insertError) {
      console.error('Failed to store analysis:', insertError)
      // Continue anyway, return the analysis
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        analysis: analysisRecord || { ai_analysis: analysis },
        cached: false 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error: any) {
    console.error('Analysis error:', error)
    return new Response(
      JSON.stringify({ 
        error: (error as Error).message || 'Analysis failed',
        success: false 
      }),
      { 
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function extractTextFromPDF(pdfData: Blob): Promise<string> {
  // Simplified PDF text extraction
  // In production, use a proper PDF parsing library
  try {
    const arrayBuffer = await pdfData.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)
    
    // Use unpdf to extract text
    const pdf = await getDocumentProxy(uint8Array);
    const { text } = await extractTextFromPdfLib(pdf, { mergePages: true });
    
    return text.trim();
  } catch (error: any) {
    console.error('PDF text extraction error:', error);
    throw new Error(`Failed to extract text from PDF using unpdf: ${(error as Error).message}`)
  }
}

async function analyzeLabResultWithAI(text: string): Promise<AnalysisResult> {
  const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
  
  if (!openaiApiKey) {
    throw new Error('OpenAI API key not configured')
  }

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: `You are a medical AI assistant specialized in lab result analysis. 
            Analyze the provided lab results and return a JSON response with the following structure:
            {
              "summary": "Brief overview of the lab results",
              "keyFindings": ["Finding 1", "Finding 2", "Finding 3"],
              "recommendations": ["Recommendation 1", "Recommendation 2"],
              "riskFactors": ["Risk factor 1", "Risk factor 2"]
            }
            
            Focus on:
            - Identifying values outside normal ranges
            - Explaining what abnormal values might indicate
            - Providing general health insights
            - Suggesting follow-up actions
            
            Always remind users to consult healthcare professionals for medical advice.
            Keep explanations clear and accessible to non-medical users.`
          },
          {
            role: 'user',
            content: `Please analyze this lab result text and provide insights: ${text.substring(0, 4000)}`
          }
        ],
        max_tokens: 1500,
        temperature: 0.3,
      }),
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    const content = data.choices[0]?.message?.content

    if (!content) {
      throw new Error('No response from OpenAI')
    }

    // Try to parse as JSON, fallback to text analysis
    let analysisResult: AnalysisResult
    try {
      const parsed = JSON.parse(content)
      analysisResult = {
        summary: parsed.summary || content,
        keyFindings: parsed.keyFindings || [],
        recommendations: parsed.recommendations || [],
        riskFactors: parsed.riskFactors || [],
        timestamp: new Date().toISOString(),
        model_used: 'gpt-4-turbo-preview'
      }
    } catch {
      // Fallback if JSON parsing fails
      analysisResult = {
        summary: content,
        keyFindings: [],
        recommendations: [],
        riskFactors: [],
        timestamp: new Date().toISOString(),
        model_used: 'gpt-4-turbo-preview'
      }
    }

    return analysisResult

  } catch (error: any) {
    console.error('OpenAI API error:', error)
    throw new Error(`Failed to analyze lab results with AI: ${(error as Error).message}`)
  }
}