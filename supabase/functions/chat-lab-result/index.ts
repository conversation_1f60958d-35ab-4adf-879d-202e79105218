import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { labResultId, message, chatHistory = [] } = await req.json()

    if (!labResultId || !message) {
      return new Response(JSON.stringify({
        error: 'Lab result ID and message are required'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authenticated user from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('Authorization header is required')
    }

    // Extract user ID from JWT token
    const token = authHeader.replace('Bearer ', '')
    let userId: string
    try {
      // Decode JWT to get user ID from 'sub' claim
      const payload = JSON.parse(atob(token.split('.')[1]))
      userId = payload.sub
      if (!userId) {
        throw new Error('User ID not found in token')
      }
    } catch (error) {
      throw new Error('Invalid authorization token')
    }

    // 1. Verify lab result ownership and get analysis
    const { data: analysis, error: analysisError } = await supabase
      .from('lab_result_analyses')
      .select(`
        *,
        labresults!inner(user_id)
      `)
      .eq('lab_result_id', labResultId)
      .eq('labresults.user_id', userId) // Verify ownership
      .single()

    if (analysisError || !analysis) {
      throw new Error('Lab result analysis not found or access denied')
    }

    // 2. Get or create chat session
    let { data: session } = await supabase
      .from('lab_result_chat_sessions')
      .select('id')
      .eq('lab_result_id', labResultId)
      .single()

    if (!session) {
      const { data: newSession, error: sessionError } = await supabase
        .from('lab_result_chat_sessions')
        .insert({
          lab_result_id: labResultId,
          user_id: userId
        })
        .select('id')
        .single()

      if (sessionError) {
        throw new Error('Failed to create chat session')
      }
      session = newSession
    }

    // 3. Save user message
    await supabase
      .from('lab_result_chat_messages')
      .insert({
        session_id: session.id,
        role: 'user',
        content: message
      })

    // 4. Generate AI response
    const aiResponse = await generateChatResponse(analysis, message, chatHistory)

    // 5. Save AI response
    await supabase
      .from('lab_result_chat_messages')
      .insert({
        session_id: session.id,
        role: 'assistant',
        content: aiResponse
      })

    // 6. Return simple JSON response (production-ready)
    return new Response(
      JSON.stringify({
        content: aiResponse,
        success: true
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Chat error:', error)
    return new Response(
      JSON.stringify({
        error: error.message || 'Chat failed',
        success: false
      }),
      {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function generateChatResponse(
  analysis: any,
  userMessage: string,
  chatHistory: ChatMessage[]
): Promise<string> {
  const openaiApiKey = Deno.env.get('OPENAI_API_KEY')

  if (!openaiApiKey) {
    throw new Error('OpenAI API key not configured')
  }

  try {
    const systemPrompt = `You are a medical AI assistant helping users understand their lab results.

Lab Analysis Context:
${JSON.stringify(analysis.ai_analysis, null, 2)}

Guidelines:
- Answer questions about the lab results clearly and accurately
- Use the analysis context to provide specific insights
- Always remind users to consult healthcare professionals for medical advice
- Keep explanations accessible to non-medical users
- If asked about values not in the analysis, acknowledge the limitation
- Be supportive and informative, not alarming`

    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...chatHistory.slice(-10), // Keep last 10 messages for context
      { role: 'user', content: userMessage }
    ]

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-turbo-preview',
        messages,
        max_tokens: 800,
        temperature: 0.3,
        stream: false, // We'll handle streaming on our end
      }),
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    const content = data.choices[0]?.message?.content

    if (!content) {
      throw new Error('No response from OpenAI')
    }

    return content

  } catch (error) {
    console.error('OpenAI API error:', error)
    throw new Error('Failed to generate chat response')
  }
}