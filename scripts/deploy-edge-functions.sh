#!/bin/bash

# Deploy Supabase Edge Functions for AI Lab Analysis
# Make sure you have Supabase CLI installed and logged in

echo "🚀 Deploying AI Lab Analysis Edge Functions..."

# Deploy analyze-lab-result function
echo "📊 Deploying analyze-lab-result function..."
supabase functions deploy analyze-lab-result

# Deploy chat-lab-result function
echo "💬 Deploying chat-lab-result function..."
supabase functions deploy chat-lab-result

echo "🔐 Setting up environment variables..."
echo "Please set the following secrets in your Supabase dashboard:"
echo "1. OPENAI_API_KEY=your_openai_api_key"
echo "2. SUPABASE_URL=your_supabase_url"
echo "3. SUPABASE_SERVICE_ROLE_KEY=your_service_role_key"

echo ""
echo "Or use the CLI:"
echo "supabase secrets set OPENAI_API_KEY=your_openai_api_key"
echo "supabase secrets set SUPABASE_URL=your_supabase_url"
echo "supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key"

echo ""
echo "📋 Don't forget to:"
echo "1. Run the database migration: supabase db push"
echo "2. Update your database types: supabase gen types typescript --local > types/database.types.ts"
echo "3. Test the functions in your Supabase dashboard"

echo ""
echo "✅ Deployment script completed!"