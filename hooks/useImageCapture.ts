import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { useOpenAIImageOcr, OpenAIOcrResults as ExtractedOcrData } from '@/hooks/useOpenAIImageOcr';

// Interface for the data returned when an image is successfully captured and processed
export interface ImageCaptureResult {
  uri: string;
  name?: string;
  type?: string; // MIME type
  ocrText?: string; // Raw OCR text from OpenAI
  ocrResults?: ExtractedOcrData; // Structured OCR data
}

// Props for the useImageCapture hook
export interface UseImageCaptureProps {
  onImageCaptured: (result: ImageCaptureResult) => void;
  onImageRemoved?: () => void;
  onError?: (error: Error) => void;
  initialImage?: string | null;
  enableOcr?: boolean;
  manipulationOptions?: {
    resizeWidth?: number;
    compressQuality?: number; // 0-1
    format?: ImageManipulator.SaveFormat;
  };
  pickerOptions?: {
    allowsEditing?: boolean;
    mediaTypes?: ImagePicker.MediaTypeOptions;
    quality?: number; // 0-1, for picker
    aspect?: [number, number]; // For picker's crop aspect ratio
  };
}

// Return type of the useImageCapture hook
export interface UseImageCaptureReturn {
  imageUri: string | null;
  isLoading: boolean; // True if picking, manipulating, or OCRing
  ocrData: ExtractedOcrData | null;
  rawOcrText: string | undefined;
  error: Error | null;
  isChangingImage: boolean;
  wasInitialImageRemoved: boolean;
  previewLoadError: boolean;
  setPreviewLoadError: (hasError: boolean) => void;
  isViewerVisible: boolean;
  actions: {
    takePhoto: () => Promise<void>;
    chooseFromLibrary: () => Promise<void>;
    removeImage: () => void;
    setIsChangingImage: (value: boolean) => void;
    openImageViewer: () => void;
    closeImageViewer: () => void;
    clearError: () => void;
  };
}

const DEFAULT_MANIPULATION_OPTIONS = {
  resizeWidth: 1024,
  compressQuality: 0.7,
  format: ImageManipulator.SaveFormat.JPEG,
};

const DEFAULT_PICKER_OPTIONS = {
  allowsEditing: true,
  mediaTypes: ImagePicker.MediaTypeOptions.Images,
  quality: 0.8, // Image picker quality
};

interface OcrFullResultType {
    rawText?: string;
    extractedData?: ExtractedOcrData;
}

export function useImageCapture({
  onImageCaptured,
  onImageRemoved,
  onError,
  initialImage,
  enableOcr = true,
  manipulationOptions: customManipulationOptions,
  pickerOptions: customPickerOptions,
}: UseImageCaptureProps): UseImageCaptureReturn {
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isViewerVisible, setIsViewerVisible] = useState(false);
  const [wasInitialImageRemoved, setWasInitialImageRemoved] = useState(false);
  const [previewLoadError, setPreviewLoadError] = useState(false);
  const [isChangingImage, setIsChangingImage] = useState(false);
  const [currentOcrResult, setCurrentOcrResult] = useState<OcrFullResultType | null>(null); // State for OCR results

  const {
    performOcr,
    isProcessingOcr,
    ocrError: ocrHookError,
  } = useOpenAIImageOcr();
  
  const manipulationOptions = { ...DEFAULT_MANIPULATION_OPTIONS, ...customManipulationOptions };
  const pickerOptions = { ...DEFAULT_PICKER_OPTIONS, ...customPickerOptions };

  useEffect(() => {
    if (initialImage) {
      setImageUri(initialImage);
      setWasInitialImageRemoved(false);
      setPreviewLoadError(false);
      setIsChangingImage(false);
      setError(null);
      setCurrentOcrResult(null); // Reset OCR data if initial image changes
    } else {
      if (imageUri && !wasInitialImageRemoved) {
         setImageUri(null);
         setCurrentOcrResult(null); // Reset OCR data
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialImage]); // imageUri and wasInitialImageRemoved are intentionally omitted as deps to avoid loops with their own setters

  useEffect(() => {
    if (ocrHookError) {
      setError(ocrHookError);
      onError?.(ocrHookError);
    }
  }, [ocrHookError, onError]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const requestPermissions = async (): Promise<boolean> => {
    clearError();
    const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
    const libraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraPermission.status !== 'granted' || libraryPermission.status !== 'granted') {
      const permError = new Error('Camera and Photo Library access are required.');
      setError(permError);
      onError?.(permError);
      Alert.alert('Permission needed', permError.message);
      return false;
    }
    return true;
  };

  const _manipulateImage = async (originalUri: string): Promise<ImageManipulator.ImageResult | null> => {
    console.log('Starting image manipulation for:', originalUri);
    setIsProcessingImage(true);
    setError(null);
    try {
      const result = await ImageManipulator.manipulateAsync(
        originalUri,
        [{ resize: { width: manipulationOptions.resizeWidth } }],
        { 
          compress: manipulationOptions.compressQuality,
          format: manipulationOptions.format,
          base64: false
        }
      );
      console.log('Image manipulation successful:', result.uri);
      return result;
    } catch (e) {
      console.error('Error manipulating image:', e);
      const manipError = e instanceof Error ? e : new Error('Failed to manipulate image');
      setError(manipError);
      onError?.(manipError);
      return null;
    } finally {
      setIsProcessingImage(false);
    }
  };

  const processImageSelection = async (pickerResult: ImagePicker.ImagePickerResult) => {
    if (pickerResult.canceled || !pickerResult.assets || pickerResult.assets.length === 0) {
      if (isChangingImage) setIsChangingImage(false);
      setIsProcessingImage(false); // Ensure loading state is reset if picker is cancelled
      return;
    }
    
    // At this point, picker was not cancelled, isProcessingImage should reflect manipulation
    const originalAsset = pickerResult.assets[0];
    const manipulatedImage = await _manipulateImage(originalAsset.uri);
    
    if (!manipulatedImage) {
        if (isChangingImage) setIsChangingImage(false);
        // Error is set by _manipulateImage, isProcessingImage is reset by _manipulateImage finally block
        return;
    }

    const processedUri = manipulatedImage.uri;
    const originalFileName = originalAsset.fileName || (pickerResult.assets[0].uri.includes('Camera') ? 'camera_image.jpg' : 'selected_image.jpg');
    const baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.')) || originalFileName;
    const finalName = `${baseName}.${manipulationOptions.format.toLowerCase()}`;

    setImageUri(processedUri);
    setWasInitialImageRemoved(false);
    setPreviewLoadError(false);
    setIsChangingImage(false);
    setError(null);
    setCurrentOcrResult(null); // Reset previous OCR data before new scan

    let ocrResultForCallback: OcrFullResultType = {};
    if (enableOcr) {
      const ocrScanResult = await performOcr(processedUri);
      if (ocrScanResult) {
        setCurrentOcrResult(ocrScanResult); // Set state for the hook's return value
        ocrResultForCallback = ocrScanResult;
      }
      // ocrHookError is handled by useEffect
    }

    onImageCaptured({
      uri: processedUri,
      name: finalName,
      type: `image/${manipulationOptions.format.toLowerCase()}`,
      ocrText: ocrResultForCallback.rawText,
      ocrResults: ocrResultForCallback.extractedData,
    });
  };

  const launchImagePicker = async (launchType: 'camera' | 'library') => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    // Set a general loading state before picker launch, as picker itself can take time.
    // isProcessingImage is for manipulation, isProcessingOcr for OCR.
    // We will use isProcessingImage to also cover the picker launch time.
    setIsProcessingImage(true); 
    clearError();

    try {
      const pickerFunc = launchType === 'camera' ? ImagePicker.launchCameraAsync : ImagePicker.launchImageLibraryAsync;
      const result = await pickerFunc({
        mediaTypes: pickerOptions.mediaTypes,
        allowsEditing: pickerOptions.allowsEditing,
        quality: pickerOptions.quality,
        aspect: pickerOptions.aspect,
      });
      // processImageSelection will handle setIsProcessingImage(false) via _manipulateImage's finally block
      // or if it returns early due to cancellation.
      await processImageSelection(result);
    } catch (e) {
      console.error(`Error ${launchType === 'camera' ? 'taking photo' : 'selecting image'}:`, e);
      const pickerError = e instanceof Error ? e : new Error(`Failed to ${launchType === 'camera' ? 'take photo' : 'select image'}`);
      setError(pickerError);
      onError?.(pickerError);
      if (isChangingImage) setIsChangingImage(false);
      setIsProcessingImage(false); // Ensure reset if picker throws directly
    }
  };

  const handleTakePhoto = () => launchImagePicker('camera');
  const handleChooseFromLibrary = () => launchImagePicker('library');

  const handleRemoveImage = () => {
    const oldImageUri = imageUri;
    setImageUri(null);
    setPreviewLoadError(false);
    setIsChangingImage(false);
    setError(null);
    setCurrentOcrResult(null); // Clear OCR data on removal
    if (initialImage && oldImageUri === initialImage) {
      setWasInitialImageRemoved(true);
    }
    onImageRemoved?.();
  };

  const openImageViewer = () => setIsViewerVisible(true);
  const closeImageViewer = () => setIsViewerVisible(false);

  const combinedIsLoading = isProcessingImage || isProcessingOcr;

  return {
    imageUri,
    isLoading: combinedIsLoading,
    ocrData: currentOcrResult?.extractedData || null, // Use state for OCR data
    rawOcrText: currentOcrResult?.rawText,
    error,
    isChangingImage,
    wasInitialImageRemoved,
    previewLoadError,
    setPreviewLoadError,
    isViewerVisible,
    actions: {
      takePhoto: handleTakePhoto,
      chooseFromLibrary: handleChooseFromLibrary,
      removeImage: handleRemoveImage,
      setIsChangingImage,
      openImageViewer,
      closeImageViewer,
      clearError,
    },
  };
} 