import React, { useState, useRef } from 'react';
import { View, Text, ActivityIndicator, Pressable } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { WebView, WebViewNavigation, WebViewMessageEvent } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import * as Clipboard from 'expo-clipboard';
import * as Print from 'expo-print';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { Button } from '@/components/ui/Button';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function LabResultWebViewScreen() {
  const params = useLocalSearchParams<{ 
    websiteUrl: string; 
    patientId?: string; 
    password?: string; 
    labResultId?: string;
  }>();
  const { websiteUrl, patientId, password, labResultId } = params;
  const { showSuccess, showError, confirm } = useAppAlerts();

  const webViewRef = useRef<WebView>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [isCapturingPdf, setIsCapturingPdf] = useState(false);

  const handleCopyToClipboard = async (textToCopy: string | undefined, type: string) => {
    if (!textToCopy) return;
    try {
      await Clipboard.setStringAsync(textToCopy);
      showSuccess(`${type} copied to clipboard!`);
    } catch (e) {
      showError(`Failed to copy ${type}.`);
      console.error('Clipboard Error:', e);
    }
  };

  const handleNavigationStateChange = (navState: WebViewNavigation) => {
    setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);
  };

  const handleSaveAsPdf = async () => {
    if (!webViewRef.current || !labResultId) {
      showError('Cannot save PDF at this time. Missing WebView reference or Lab Result ID.');
      return;
    }
    setIsCapturingPdf(true);
    try {
      webViewRef.current.injectJavaScript('window.ReactNativeWebView.postMessage(document.documentElement.outerHTML);');
    } catch (e: any) {
      console.error('Error injecting JS for PDF capture:', e);
      showError('Failed to initiate PDF capture. ' + e.message);
      setIsCapturingPdf(false);
    }
  };

  const handleWebViewMessage = async (event: WebViewMessageEvent) => {
    const htmlString = event.nativeEvent.data;
    try {
      const { uri: pdfUri } = await Print.printToFileAsync({ html: htmlString });
      showSuccess('PDF captured successfully!');
      
      if (labResultId) {
        router.replace({
          pathname: '/modals/modal-labresult',
          params: {
            id: labResultId,
            newAttachmentUri: pdfUri,
            attachmentAction: 'attachCapturedPdf',
          },
        });
      } else {
        showError('Lab Result ID not found, cannot attach PDF.');
      }
    } catch (e: any) {
      console.error('Error generating or saving PDF:', e);
      showError('Failed to generate PDF. ' + e.message);
    } finally {
      setIsCapturingPdf(false);
    }
  };

  if (!websiteUrl) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center p-5 bg-background dark:bg-neutral-900">
        <Stack.Screen options={{ title: 'Error' }} />
        <Ionicons name="alert-circle-outline" size={48} className="text-destructive dark:text-red-400" />
        <Text className="text-lg text-destructive dark:text-red-400 text-center mt-2.5">
          Website URL is missing.
        </Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
      <Stack.Screen options={{ title: 'Lab Results Portal' }} />

      {(patientId || password) && (
        <View className="flex-row justify-around items-center py-2.5 px-2 border-b border-border dark:border-neutral-700 bg-card dark:bg-neutral-800">
          {patientId && (
            <Button
              title="Copy Patient ID"
              onPress={() => handleCopyToClipboard(patientId, 'Patient ID')}
              variant="outline"
              icon="person-outline"
              style={{ flex: 1, marginHorizontal: 4 }}
            />
          )}
          {password && (
            <Button
              title="Copy Password"
              onPress={() => handleCopyToClipboard(password, 'Password')}
              variant="outline"
              icon="key-outline"
              style={{ flex: 1, marginHorizontal: 4 }}
            />
          )}
        </View>
      )}

      <WebView
        ref={webViewRef}
        source={{ uri: websiteUrl }}
        onLoadStart={() => {
          setIsLoading(true);
          setError(null);
        }}
        onLoadEnd={() => setIsLoading(false)}
        onError={(syntheticEvent) => {
          setIsLoading(false);
          const { nativeEvent } = syntheticEvent;
          setError(nativeEvent.description || 'Failed to load the page.');
          console.warn('WebView error: ', nativeEvent);
        }}
        onNavigationStateChange={handleNavigationStateChange}
        onMessage={handleWebViewMessage}
        javaScriptEnabled={true}
        className="flex-1"
      />

      <View className="flex-row justify-around items-center py-2 px-2 border-t border-border dark:border-neutral-700 bg-card dark:bg-neutral-800">
        <Pressable onPress={() => webViewRef.current?.goBack()} disabled={!canGoBack || isCapturingPdf} className="p-2">
          <Ionicons name="arrow-back" size={24} className={!canGoBack || isCapturingPdf ? "text-muted-foreground/50 dark:text-neutral-600" : "text-primary dark:text-sky-400"} />
        </Pressable>
        <Pressable onPress={() => webViewRef.current?.goForward()} disabled={!canGoForward || isCapturingPdf} className="p-2">
          <Ionicons name="arrow-forward" size={24} className={!canGoForward || isCapturingPdf ? "text-muted-foreground/50 dark:text-neutral-600" : "text-primary dark:text-sky-400"} />
        </Pressable>
        <Pressable onPress={() => webViewRef.current?.reload()} disabled={isCapturingPdf} className="p-2">
          <Ionicons name="refresh" size={24} className={isCapturingPdf ? "text-muted-foreground/50 dark:text-neutral-600" : "text-primary dark:text-sky-400"} />
        </Pressable>
        <Pressable onPress={handleSaveAsPdf} disabled={isCapturingPdf || isLoading} className="p-2">
          {isCapturingPdf ? (
            <ActivityIndicator size="small" className="text-primary dark:text-sky-400" />
          ) : (
            <Ionicons name="download-outline" size={24} className={isLoading ? "text-muted-foreground/50 dark:text-neutral-600" : "text-primary dark:text-sky-400"} />
          )}
        </Pressable>
      </View>

      {(isLoading && !isCapturingPdf) && !error && (
        <View className="absolute inset-0 bg-background/80 dark:bg-neutral-900/80 justify-center items-center">
          <ActivityIndicator size="large" className="text-primary" />
          <Text className="mt-2.5 text-base text-foreground dark:text-neutral-300">
            Loading website...
          </Text>
        </View>
      )}

      {isCapturingPdf && (
        <View className="absolute inset-0 bg-background/90 dark:bg-neutral-900/90 justify-center items-center z-50">
          <ActivityIndicator size="large" className="text-primary" />
          <Text className="mt-2.5 text-base text-foreground dark:text-neutral-300">
            Capturing PDF...
          </Text>
        </View>
      )}

      {error && (
        <View className="absolute inset-0 bg-background dark:bg-neutral-900 justify-center items-center p-5">
           <Ionicons name="cloud-offline-outline" size={32} className="text-muted-foreground dark:text-neutral-500" />
          <Text className="text-base text-center mt-2.5 mb-4 text-foreground dark:text-neutral-300">
            {error}
          </Text>
          <Pressable 
            onPress={() => webViewRef.current?.reload()} 
            disabled={isCapturingPdf}
            className={`py-2 px-4 rounded bg-primary active:bg-primary/90 ${isCapturingPdf ? 'opacity-50' : ''}`}
          >
            <Text className="text-primary-foreground text-sm">Retry</Text>
          </Pressable>
        </View>
      )}
    </SafeAreaView>
  );
} 