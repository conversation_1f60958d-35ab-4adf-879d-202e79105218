import React from 'react';
import { View, Text, Pressable, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SuggestedQuestionsProps {
  onQuestionPress: (question: string) => void;
  disabled?: boolean;
}

const SUGGESTED_QUESTIONS = [
  "What do my cholesterol levels mean?",
  "Are any of my values concerning?",
  "How do these results compare to normal ranges?",
  "What lifestyle changes should I consider?",
  "Should I be worried about any specific values?",
  "What do these abbreviations mean?",
  "When should I get my next lab work done?",
  "Can you explain the key findings?"
];

export const SuggestedQuestions: React.FC<SuggestedQuestionsProps> = ({ 
  onQuestionPress, 
  disabled = false 
}) => {
  return (
    <View className="px-4 py-3 border-t border-border dark:border-neutral-700 bg-muted/30 dark:bg-neutral-800/30">
      <Text className="text-sm font-medium text-muted-foreground dark:text-neutral-400 mb-3">
        Suggested questions:
      </Text>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        className="flex-row"
        contentContainerStyle={{ paddingRight: 16 }}
      >
        {SUGGESTED_QUESTIONS.map((question, index) => (
          <Pressable
            key={index}
            onPress={() => !disabled && onQuestionPress(question)}
            disabled={disabled}
            className={`
              mr-3 px-4 py-2 rounded-full border
              ${disabled 
                ? 'border-muted bg-muted/50 opacity-50' 
                : 'border-border dark:border-neutral-600 bg-card dark:bg-neutral-800 active:bg-muted dark:active:bg-neutral-700'
              }
            `}
          >
            <View className="flex-row items-center">
              <Ionicons 
                name="help-circle-outline" 
                size={14} 
                className="text-muted-foreground dark:text-neutral-400 mr-2" 
              />
              <Text className={`
                text-sm 
                ${disabled 
                  ? 'text-muted-foreground/50' 
                  : 'text-foreground dark:text-neutral-200'
                }
              `}>
                {question}
              </Text>
            </View>
          </Pressable>
        ))}
      </ScrollView>
    </View>
  );
};

export default SuggestedQuestions;