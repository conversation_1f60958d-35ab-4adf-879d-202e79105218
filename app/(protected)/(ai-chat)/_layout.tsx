import React from 'react';
import { View, Text, ScrollView, Pressable } from 'react-native';
import { Drawer } from 'expo-router/drawer';
import { router } from 'expo-router';
import { useLabResults } from '@/hooks/entities/useLabResults';

// Custom drawer content showing previous chat sessions
function CustomDrawerContent() {
  const { useFetchLabResults } = useLabResults();
  const { data: labResults = [], isLoading } = useFetchLabResults();

  // Filter lab results that have been analyzed (have AI analysis)
  const analyzedResults = labResults.filter(result =>
    result.captured_pdf_path && result.ai_analysis
  );

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900 pt-12">
      <View className="px-4 py-6 border-b border-border dark:border-neutral-700">
        <Text className="text-lg font-semibold text-foreground dark:text-neutral-200">
          AI Chat Sessions
        </Text>
        <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1">
          Previous lab result conversations
        </Text>
      </View>

      <ScrollView className="flex-1">
        {analyzedResults.map((result) => (
          <Pressable
            key={result.id}
            onPress={() => router.push({
              pathname: '/(ai-chat)/lab-result-ai-chat' as any,
              params: { labResultId: result.id }
            })}
            className="px-4 py-3 border-b border-border/50 dark:border-neutral-700/50 active:bg-muted dark:active:bg-neutral-800"
          >
            <View className="flex-row items-center">
              <View className="flex-1">
                <Text className="font-medium text-foreground dark:text-neutral-200">
                  {result.lab_name}
                </Text>
                <Text className="text-sm text-muted-foreground dark:text-neutral-400">
                  {new Date(result.result_date).toLocaleDateString()}
                </Text>
              </View>
            </View>
          </Pressable>
        ))}

        {analyzedResults.length === 0 && (
          <View className="px-4 py-8 items-center">
            <Text className="text-muted-foreground dark:text-neutral-400 text-center">
              No chat sessions yet
            </Text>
            <Text className="text-sm text-muted-foreground dark:text-neutral-500 text-center mt-2">
              Analyze a lab result to start chatting with AI
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const DrawerLayout = () => {
  return (
    <Drawer
      drawerContent={CustomDrawerContent}
      screenOptions={{
        drawerStyle: {
          width: 300,
        },
        headerShown: true,
      }}
    >
      <Drawer.Screen
        name="lab-result-ai-chat"
        options={{
          title: 'AI Lab Analysis',
          drawerLabel: 'Current Chat',
        }}
      />
    </Drawer>
  );
};

export default DrawerLayout;