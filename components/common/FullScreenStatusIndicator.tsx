import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { Button } from '../ui/Button'; // Assuming Button component path

interface FullScreenStatusIndicatorProps {
  isLoading?: boolean;
  error?: Error | { message: string } | null;
  loadingMessage?: string;
  errorMessage?: string; // Overrides error.message if provided
  onRetry?: () => void;
  noData?: boolean; // Optional: to display a "no data" message
  noDataMessage?: string;
}

const FullScreenStatusIndicator: React.FC<FullScreenStatusIndicatorProps> = ({
  isLoading = false,
  error = null,
  loadingMessage = 'Loading...',
  errorMessage,
  onRetry,
  noData = false,
  noDataMessage = 'No data available.',
}) => {
  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" className="text-primary" />
        {loadingMessage && <Text style={styles.message} className="mt-4 text-muted-foreground dark:text-neutral-400">{loadingMessage}</Text>}
      </View>
    );
  }

  if (error) {
    const displayErrorMessage = errorMessage || (error as any)?.message || 'An unexpected error occurred.';
    return (
      <View style={styles.container}>
        <Text style={styles.errorMessage} className="text-destructive dark:text-red-400 mb-4 text-center">{displayErrorMessage}</Text>
        {onRetry && <Button title="Retry" onPress={onRetry} variant="outline" />}
      </View>
    );
  }

  if (noData) {
    return (
      <View style={styles.container}>
        <Text style={styles.message} className="text-muted-foreground dark:text-neutral-400">{noDataMessage}</Text>
      </View>
    );
  }

  return null; // Return null if no status needs to be displayed
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    // Ensure it has a background to overlay content if used that way
    // Consider adding className="bg-background dark:bg-neutral-900" if it won't inherit
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
});

export default FullScreenStatusIndicator; 