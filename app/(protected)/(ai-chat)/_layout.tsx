import React from 'react';
import { View, Text, ScrollView, Pressable, ActivityIndicator } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Drawer } from 'expo-router/drawer';
import { useTheme } from '@react-navigation/native';
import { router } from 'expo-router';
import { useLabResults } from '@/hooks/entities/useLabResults';

function CustomDrawerContent() {
  const theme = useTheme();
  const { useFetchLabResults } = useLabResults();
  const { data: labResults = [], isLoading } = useFetchLabResults();

  // Filter lab results that have PDFs (can be analyzed)
  const analyzableResults = labResults.filter(result => result.captured_pdf_path);

  if (isLoading) {
    return (
      <View className="flex-1 bg-background dark:bg-neutral-900 justify-center items-center">
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text className="mt-4 text-muted-foreground">Loading sessions...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900 pt-12">
      <View className="px-4 py-6 border-b border-border dark:border-neutral-700">
        <Text className="text-lg font-semibold text-foreground dark:text-neutral-200">
          Lab Analysis Sessions
        </Text>
        <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1">
          Previous conversations
        </Text>
      </View>

      <ScrollView className="flex-1">
        <Pressable
          onPress={() => router.push('/(ai-chat)')}
          className="px-4 py-3 border-b border-border/50 dark:border-neutral-700/50 active:bg-muted dark:active:bg-neutral-800"
        >
          <Text className="font-medium text-foreground dark:text-neutral-200">
            New Chat
          </Text>
          <Text className="text-sm text-muted-foreground dark:text-neutral-400">
            Start a new analysis
          </Text>
        </Pressable>

        {analyzableResults.map((result) => (
          <Pressable
            key={result.id}
            onPress={() => router.push(`/(ai-chat)?labResultId=${result.id}`)}
            className="px-4 py-3 border-b border-border/50 dark:border-neutral-700/50 active:bg-muted dark:active:bg-neutral-800"
          >
            <Text className="font-medium text-foreground dark:text-neutral-200">
              {result.lab_name}
            </Text>
            <Text className="text-sm text-muted-foreground dark:text-neutral-400">
              {result.result_date ? new Date(result.result_date).toLocaleDateString() : 'No date'}
            </Text>
          </Pressable>
        ))}

        {analyzableResults.length === 0 && (
          <View className="px-4 py-8 items-center">
            <Text className="text-muted-foreground dark:text-neutral-400 text-center">
              No lab results available
            </Text>
            <Text className="text-sm text-muted-foreground dark:text-neutral-500 text-center mt-2">
              Upload lab results with PDFs to start analyzing
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

export default function ChatLayout() {
  const theme = useTheme();

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        drawerContent={CustomDrawerContent}
        screenOptions={{
          drawerStyle: {
            backgroundColor: theme.colors.card,
            width: 300,
          },
          headerStyle: {
            backgroundColor: theme.colors.card,
          },
          headerTintColor: theme.colors.text,
          drawerActiveTintColor: theme.colors.primary,
          drawerInactiveTintColor: theme.colors.text,
        }}
      >
        <Drawer.Screen
          name="index"
          options={{
            drawerLabel: 'Chat',
            title: 'AI Lab Analysis',
          }}
        />
      </Drawer>
    </GestureHandlerRootView>
  );
}