import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import type { ChatMessage as ChatMessageType } from '@/hooks/useLabResultAI';

interface ChatMessageProps {
  message: ChatMessageType;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.role === 'user';
  
  return (
    <View className={`mb-4 ${isUser ? 'items-end' : 'items-start'}`}>
      <View className={`max-w-[85%] p-3 rounded-2xl ${
        isUser 
          ? 'bg-primary rounded-br-md' 
          : 'bg-muted dark:bg-neutral-800 rounded-bl-md'
      }`}>
        {!isUser && (
          <View className="flex-row items-center mb-2">
            <Ionicons 
              name="sparkles" 
              size={16} 
              className="text-primary dark:text-sky-400 mr-2" 
            />
            <Text className="text-xs font-medium text-primary dark:text-sky-400">
              AI Assistant
            </Text>
          </View>
        )}
        
        <Text className={`text-base leading-6 ${
          isUser 
            ? 'text-primary-foreground' 
            : 'text-foreground dark:text-neutral-200'
        }`}>
          {message.content}
        </Text>
        
        <Text className={`text-xs mt-2 ${
          isUser 
            ? 'text-primary-foreground/70' 
            : 'text-muted-foreground dark:text-neutral-400'
        }`}>
          {message.timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </Text>
      </View>
    </View>
  );
};

export default ChatMessage;