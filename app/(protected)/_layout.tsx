import { Redirect, router, Stack } from 'expo-router';
import { useAuth } from '@clerk/clerk-expo';
import { useWindowDimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';

const ProtectedLayout = () => {
  
  const {isSignedIn} = useAuth();
  const {height} = useWindowDimensions();
  const theme = useTheme();
  
  if (!isSignedIn) {
    return <Redirect href="/sign-in" />;
  }

  return (
    <Stack screenOptions={{ 
      headerShown: false, 
      headerLeft: () => (
        <Ionicons 
          name="arrow-back-outline" 
          size={24} 
          color={theme.colors.text} 
          onPress={() => router.back()} 
          style={{ marginLeft: 10}} 
        />
      ),
    }}>
      <Stack.Screen name="(tabs)"/>
      <Stack.Screen name="modals/modal-labresult" options={{
        headerShown: true,
        headerTransparent: true,
      }}/>
      <Stack.Screen name="modals/modal-prescription" options={{
        headerShown: true,
        headerTransparent: true,
      }}/>
      <Stack.Screen name="modals/modal-patient" options={{
        headerShown: true,
        presentation: 'fullScreenModal',
      }}/>
      <Stack.Screen name="modals/modal-medicine" options={{
        headerShown: true,
        headerTransparent: true,
      }}/>
      <Stack.Screen name="modals/modal-doctor" options={{
        headerShown: true,
      }}/>
      <Stack.Screen name="modals/modal-filter" options={{
        presentation: 'formSheet',
        headerShown: false,
        sheetAllowedDetents: height > 700 ? [0.22] : "fitToContents",
        sheetGrabberVisible: false,
        sheetExpandsWhenScrolledToEdge: false,
        sheetCornerRadius: 10,
      }}/>
      <Stack.Screen name="modals/lab-result-webview" options={{
        headerShown: true,
        presentation: 'modal',
      }}/>
      <Stack.Screen name="(ai-chat)" options={{
        headerShown: false,
        
      }}/>
      <Stack.Screen name="settings" options={{
        headerShown: true,
        headerLeft: undefined,
      }} />
      <Stack.Screen
        name="modals/router-select-screen"
        options={{
          presentation: 'formSheet',
          headerShown: true, 
          sheetGrabberVisible: true,
          headerLeft: undefined,
        }}
      />
    </Stack>
  );
}

export default ProtectedLayout;