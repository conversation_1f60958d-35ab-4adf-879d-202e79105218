import React from 'react';
import { View, Text } from 'react-native';
import { format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Card from '../ui/Card';
import { LabResultWithPatient } from '@/hooks/entities/useLabResults';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { Button } from '../ui/Button';

interface LabResultCardProps {
  result: LabResultWithPatient;
  onPress?: () => void;
}

/**
 * A card component for displaying a lab result in a list.
 */
const LabResultCard: React.FC<LabResultCardProps> = ({ result, onPress }) => {
  // Format date if available
  const formattedDate = result.result_date
    ? format(new Date(result.result_date), 'MMM d, yyyy')
    : 'No date';

  const { confirm } = useAppAlerts();

  const imagePath = result.image_path;
  const bucketId = result.image_bucket_id;

  console.log(`LabResultCard (${result.lab_name}): imagePath:`, imagePath, 'bucketId:', bucketId);

  const { url: thumbnailUrl, isLoading: isLoadingThumbnail, error: urlError } = useSupabaseStorageUrl({
    path: imagePath,
    bucketId: bucketId,
  });

  console.log(`LabResultCard (${result.lab_name}): thumbnailUrl:`, thumbnailUrl, 'isLoading:', isLoadingThumbnail, 'urlError:', urlError);

  // Check if the lab result has an attachment
  const hasAttachment = !!result.image_path;

  // Content for the card
  const content = (
    <View className="space-y-2">
      {/* Date and attachment indicator */}
      <View className="flex-row justify-between items-center">
        <Text className="text-sm text-gray-500">{formattedDate}</Text>
        {hasAttachment && (
          <View className="flex-row items-center">
            <Ionicons name="document-attach" size={16} color="#6B7280" />
            <Text className="text-sm text-gray-500 ml-1">Attachment</Text>
          </View>
        )}
      </View>

      {/* Patient info if available */}
      {result.patients && (
        <View className="flex-row items-center mt-1">
          <Ionicons name="person" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">{result.patients.name}</Text>
        </View>
      )}

      {/* Show patient name from the lab result if there's no linked patient */}
      {!result.patients && result.patient_name && (
        <View className="flex-row items-center mt-1">
          <Ionicons name="person" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-500 ml-1">{result.patient_name}</Text>
        </View>
      )}
    </View>
  );

  const handleCheckResultsPress = () => {
    if (!result.website) return;

    let url = result.website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    confirm({
      title: "Open External Website",
      message: "You are about to open the lab's website. Patient ID and password (if available on the lab result) will be passed to the next screen for your convenience. Do you want to proceed?",
      confirmText: "Proceed",
      cancelText: "Cancel",
      onConfirm: () => {
        router.push({
          pathname: '/modals/lab-result-webview',
          params: {
            websiteUrl: url,
            patientId: result.patient_id,
            password: result.password,
            labResultId: result.id,
          },
        });
      },
    });
  };

  return (
    <Card
      title={result.lab_name}
      content={content}
      onPress={onPress}
      imageUrl={thumbnailUrl}
      imageLoading={isLoadingThumbnail}
      rightAction={
        <Ionicons name="chevron-forward" size={20} color="#6B7280" />
      }
    >
      {/* Action buttons section */}
      {(result.website || hasAttachment) && (
        <View className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 space-y-2">
          {/* AI Analysis button - only show if there's a PDF attachment */}
          {result.captured_pdf_path && (
            <Button
              title="Analyze with AI"
              onPress={() => router.push({
                pathname: '/modals/modal-lab-result-ai-chat',
                params: { id: result.id }
              })}
              variant="outline"
              icon={<Ionicons name="sparkles-outline" size={16} className="text-primary dark:text-sky-400" />}
            />
          )}

          {/* Check Results button */}
          {result.website && (
            <Button
              title="Check Results"
              onPress={handleCheckResultsPress}
              variant="outline"
              icon={<Ionicons name="open-outline" size={16} className="text-primary dark:text-sky-400" />}
            />
          )}
        </View>
      )}
    </Card>
  );
};

export default LabResultCard;