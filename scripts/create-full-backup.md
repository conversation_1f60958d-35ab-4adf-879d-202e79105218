# Full Database Backup Instructions

Since <PERSON><PERSON> is not running locally, please create a full database backup manually using the Supabase Dashboard:

## Method 1: Supabase Dashboard (Recommended)

1. **Go to Supabase Dashboard**: https://supabase.com/dashboard
2. **Select your project**: Recepturko (fvgiugbeichkyobvnjle)
3. **Navigate to Settings**: Click on "Settings" in the left sidebar
4. **Go to Database section**: Click on "Database"
5. **Create Backup**:
   - Look for "Backups" or "Database Backups" section
   - Click "Create Backup" or "Download Backup"
   - This will create a full PostgreSQL dump including:
     - All tables and data
     - All functions and procedures
     - All policies and RLS rules
     - All triggers and constraints
     - All custom types and enums

## Method 2: CLI with Database Password (Alternative)

If you have the database password, you can use:

```bash
# Get database password from Supabase Dashboard > Settings > Database
# Then run:
pg_dump "postgresql://postgres:@db.fvgiugbeichkyobvnjle.supabase.co:5432/postgres" > full_backup_$(date +%Y%m%d_%H%M%S).sql

```

## What the backup includes:

- ✅ All table schemas and data
- ✅ All database functions
- ✅ All Row Level Security policies
- ✅ All triggers and constraints
- ✅ All custom types and enums
- ✅ All indexes
- ✅ All foreign key relationships
- ✅ User permissions and roles

## Backup file naming:

Save as: `recepturko_full_backup_before_ai_analysis_YYYYMMDD_HHMMSS.sql`

## After backup is created:

1. Verify the backup file size (should be substantial if you have data)
2. Store it safely (local + cloud storage recommended)
3. Confirm you can restore from it if needed
4. Then proceed with the AI analysis deployment

## Restoration (if needed):

```bash
# To restore (only if needed):
psql "postgresql://postgres:[YOUR_DB_PASSWORD]@db.fvgiugbeichkyobvnjle.supabase.co:5432/postgres" < backup_file.sql
```

**Note**: The AI analysis migration only ADDS new tables and doesn't modify existing ones, so the risk is minimal. But having a full backup is always best practice.
