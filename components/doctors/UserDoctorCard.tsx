import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Card from '../ui/Card';
import { UserDoctorWithDetails } from '@/hooks/entities/useUserDoctors'; // Import the detailed type

interface UserDoctorCardProps {
  doctor: UserDoctorWithDetails;
  onPress?: () => void;
}

/**
 * A card component for displaying user doctor information.
 */
const UserDoctorCard: React.FC<UserDoctorCardProps> = ({ doctor, onPress }) => {
  // Use the related alldoctors address if available, otherwise use the user_doctors address
  const displayAddress = doctor.alldoctors?.workplace_address || doctor.workplace_address;
  const displayFee = doctor.alldoctors?.fee;
  
  const content = (
    <View className="space-y-1">
      {/* Specialty */}
      {doctor.specialty && (
        <View className="flex-row items-center">
          <Ionicons name="medkit-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-600 ml-1">{doctor.specialty}</Text>
        </View>
      )}
      {/* Workplace */}
      {doctor.workplace && (
        <View className="flex-row items-center">
          <Ionicons name="business-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-600 ml-1">{doctor.workplace}</Text>
        </View>
      )}
      {/* Address */}
      {displayAddress && (
        <View className="flex-row items-center">
          <Ionicons name="location-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-600 ml-1">{displayAddress}</Text>
        </View>
      )}
       {/* Phone Number */}
       {doctor.phone_number && (
        <View className="flex-row items-center">
          <Ionicons name="call-outline" size={16} color="#6B7280" />
          <Text className="text-sm text-gray-600 ml-1">{doctor.phone_number}</Text>
        </View>
      )}
      {/* Fee (from alldoctors if linked) */}
      {displayFee !== null && displayFee !== undefined && (
        <View className="flex-row items-center">
          <Ionicons name="cash-outline" size={16} color="#10B981" />
          <Text className="text-sm text-gray-600 ml-1">Fee: {displayFee}</Text>
        </View>
      )}
       {/* Notes */}
       {doctor.notes && (
        <View className="flex-row items-start mt-1">
          <Ionicons name="document-text-outline" size={16} color="#6B7280" style={{ marginTop: 2 }} />
          <Text className="text-sm text-gray-600 ml-1 flex-1">Notes: {doctor.notes}</Text>
        </View>
      )}
    </View>
  );

  return (
    <Card
      title={doctor.name}
      content={content}
      onPress={onPress}
      rightAction={
        <Ionicons name="chevron-forward" size={20} color="#6B7280" />
      }
    />
  );
};

export default UserDoctorCard; 