{"name": "receptur<PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@clerk/clerk-expo": "^2.10.4", "@expo/react-native-action-sheet": "^4.1.1", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.3.12", "@react-navigation/material-top-tabs": "^7.2.10", "@react-navigation/native": "^7.0.14", "@shopify/flash-list": "1.7.6", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.75.0", "expo": "53.0.9", "expo-blur": "~14.1.4", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-media-library": "~17.1.6", "expo-print": "~14.1.4", "expo-router": "~5.0.7", "expo-secure-store": "^14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "i18next": "^25.0.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.1", "react-i18next": "^15.4.1", "react-native": "0.79.2", "react-native-calendars": "^1.1312.0", "react-native-gesture-handler": "~2.24.0", "react-native-image-viewing": "^0.2.2", "react-native-pager": "^0.0.3", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-tab-view": "^4.0.10", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.17", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "eslint": "^9.25.1", "eslint-config-expo": "~9.2.0", "jest": "^29.2.1", "jest-expo": "~53.0.5", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}