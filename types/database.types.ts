export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      alldoctors: {
        Row: {
          city: string | null
          created_at: string | null
          fee: number | null
          id: string
          name: string
          phone_number: string | null
          specialty: string | null
          working_hours: Json | null
          workplace: string | null
          workplace_address: string | null
        }
        Insert: {
          city?: string | null
          created_at?: string | null
          fee?: number | null
          id?: string
          name: string
          phone_number?: string | null
          specialty?: string | null
          working_hours?: Json | null
          workplace?: string | null
          workplace_address?: string | null
        }
        Update: {
          city?: string | null
          created_at?: string | null
          fee?: number | null
          id?: string
          name?: string
          phone_number?: string | null
          specialty?: string | null
          working_hours?: Json | null
          workplace?: string | null
          workplace_address?: string | null
        }
        Relationships: []
      }
      allmeds: {
        Row: {
          created_at: string | null
          description: string | null
          dosage_amount: number | null
          dosage_unit: Database["public"]["Enums"]["dosage_unit_enum"] | null
          duration_amount: number | null
          duration_unit:
            | Database["public"]["Enums"]["duration_unit_enum"]
            | null
          frequency_amount: number | null
          frequency_unit:
            | Database["public"]["Enums"]["frequency_unit_enum"]
            | null
          id: string
          image_bucket_id: string | null
          image_metadata: Json | null
          image_path: string | null
          name: string
          price: number | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          dosage_amount?: number | null
          dosage_unit?: Database["public"]["Enums"]["dosage_unit_enum"] | null
          duration_amount?: number | null
          duration_unit?:
            | Database["public"]["Enums"]["duration_unit_enum"]
            | null
          frequency_amount?: number | null
          frequency_unit?:
            | Database["public"]["Enums"]["frequency_unit_enum"]
            | null
          id?: string
          image_bucket_id?: string | null
          image_metadata?: Json | null
          image_path?: string | null
          name: string
          price?: number | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          dosage_amount?: number | null
          dosage_unit?: Database["public"]["Enums"]["dosage_unit_enum"] | null
          duration_amount?: number | null
          duration_unit?:
            | Database["public"]["Enums"]["duration_unit_enum"]
            | null
          frequency_amount?: number | null
          frequency_unit?:
            | Database["public"]["Enums"]["frequency_unit_enum"]
            | null
          id?: string
          image_bucket_id?: string | null
          image_metadata?: Json | null
          image_path?: string | null
          name?: string
          price?: number | null
        }
        Relationships: []
      }
      lab_result_analyses: {
        Row: {
          ai_analysis: Json | null
          created_at: string | null
          extracted_text: string | null
          id: string
          lab_result_id: string | null
          updated_at: string | null
        }
        Insert: {
          ai_analysis?: Json | null
          created_at?: string | null
          extracted_text?: string | null
          id?: string
          lab_result_id?: string | null
          updated_at?: string | null
        }
        Update: {
          ai_analysis?: Json | null
          created_at?: string | null
          extracted_text?: string | null
          id?: string
          lab_result_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lab_result_analyses_lab_result_id_fkey"
            columns: ["lab_result_id"]
            isOneToOne: false
            referencedRelation: "labresults"
            referencedColumns: ["id"]
          },
        ]
      }
      lab_result_chat_messages: {
        Row: {
          content: string
          created_at: string | null
          id: string
          role: string
          session_id: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          role: string
          session_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          role?: string
          session_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lab_result_chat_messages_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "lab_result_chat_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      lab_result_chat_sessions: {
        Row: {
          created_at: string | null
          id: string
          lab_result_id: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          lab_result_id?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          lab_result_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "lab_result_chat_sessions_lab_result_id_fkey"
            columns: ["lab_result_id"]
            isOneToOne: false
            referencedRelation: "labresults"
            referencedColumns: ["id"]
          },
        ]
      }
      labresults: {
        Row: {
          captured_pdf_bucket_id: string | null
          captured_pdf_path: string | null
          created_at: string
          id: string
          image_bucket_id: string | null
          image_path: string | null
          lab_name: string
          password: string
          patient_id: string
          patient_name: string | null
          patient_reference_id: string | null
          phone_number1: string | null
          phone_number2: string | null
          phone_number3: string | null
          result_date: string | null
          updated_at: string
          user_id: string | null
          website: string | null
        }
        Insert: {
          captured_pdf_bucket_id?: string | null
          captured_pdf_path?: string | null
          created_at?: string
          id?: string
          image_bucket_id?: string | null
          image_path?: string | null
          lab_name: string
          password: string
          patient_id: string
          patient_name?: string | null
          patient_reference_id?: string | null
          phone_number1?: string | null
          phone_number2?: string | null
          phone_number3?: string | null
          result_date?: string | null
          updated_at?: string
          user_id?: string | null
          website?: string | null
        }
        Update: {
          captured_pdf_bucket_id?: string | null
          captured_pdf_path?: string | null
          created_at?: string
          id?: string
          image_bucket_id?: string | null
          image_path?: string | null
          lab_name?: string
          password?: string
          patient_id?: string
          patient_name?: string | null
          patient_reference_id?: string | null
          phone_number1?: string | null
          phone_number2?: string | null
          phone_number3?: string | null
          result_date?: string | null
          updated_at?: string
          user_id?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "labresults_patient_reference_id_fkey"
            columns: ["patient_reference_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "labresults_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      patients: {
        Row: {
          age: number | null
          bloodtype: Database["public"]["Enums"]["blood_type_enum"] | null
          created_at: string | null
          id: string
          name: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          age?: number | null
          bloodtype?: Database["public"]["Enums"]["blood_type_enum"] | null
          created_at?: string | null
          id?: string
          name: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          age?: number | null
          bloodtype?: Database["public"]["Enums"]["blood_type_enum"] | null
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patients_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      prescriptions: {
        Row: {
          back_image_path: string | null
          created_at: string | null
          doctor_id: string | null
          front_image_path: string | null
          id: string
          image_bucket_id: string | null
          image_metadata: Json | null
          name: string
          notes: string | null
          patient_id: string | null
          prescription_date: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          back_image_path?: string | null
          created_at?: string | null
          doctor_id?: string | null
          front_image_path?: string | null
          id?: string
          image_bucket_id?: string | null
          image_metadata?: Json | null
          name: string
          notes?: string | null
          patient_id?: string | null
          prescription_date: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          back_image_path?: string | null
          created_at?: string | null
          doctor_id?: string | null
          front_image_path?: string | null
          id?: string
          image_bucket_id?: string | null
          image_metadata?: Json | null
          name?: string
          notes?: string | null
          patient_id?: string | null
          prescription_date?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "prescriptions_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "user_doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prescriptions_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prescriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string | null
          full_name: string | null
          id: string
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id: string
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id?: string
          updated_at?: string
        }
        Relationships: []
      }
      user_doctors: {
        Row: {
          city: string | null
          created_at: string | null
          doctor_id: string | null
          fee: number | null
          id: string
          is_custom: boolean | null
          name: string
          notes: string | null
          phone_number: string | null
          specialty: string | null
          updated_at: string | null
          user_id: string | null
          working_hours: Json | null
          workplace: string | null
          workplace_address: string | null
        }
        Insert: {
          city?: string | null
          created_at?: string | null
          doctor_id?: string | null
          fee?: number | null
          id?: string
          is_custom?: boolean | null
          name: string
          notes?: string | null
          phone_number?: string | null
          specialty?: string | null
          updated_at?: string | null
          user_id?: string | null
          working_hours?: Json | null
          workplace?: string | null
          workplace_address?: string | null
        }
        Update: {
          city?: string | null
          created_at?: string | null
          doctor_id?: string | null
          fee?: number | null
          id?: string
          is_custom?: boolean | null
          name?: string
          notes?: string | null
          phone_number?: string | null
          specialty?: string | null
          updated_at?: string | null
          user_id?: string | null
          working_hours?: Json | null
          workplace?: string | null
          workplace_address?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_doctors_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "alldoctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_doctors_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_meds: {
        Row: {
          created_at: string | null
          description: string | null
          dosage_amount: number | null
          dosage_unit: Database["public"]["Enums"]["dosage_unit_enum"] | null
          duration_amount: number | null
          duration_unit:
            | Database["public"]["Enums"]["duration_unit_enum"]
            | null
          expiration_date: string | null
          frequency_amount: number | null
          frequency_unit:
            | Database["public"]["Enums"]["frequency_unit_enum"]
            | null
          id: string
          image_bucket_id: string | null
          image_path: string | null
          is_custom: boolean | null
          med_id: string | null
          name: string
          notes: string | null
          opened_on_date: string | null
          patient_id: string | null
          prescription_id: string | null
          price: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          dosage_amount?: number | null
          dosage_unit?: Database["public"]["Enums"]["dosage_unit_enum"] | null
          duration_amount?: number | null
          duration_unit?:
            | Database["public"]["Enums"]["duration_unit_enum"]
            | null
          expiration_date?: string | null
          frequency_amount?: number | null
          frequency_unit?:
            | Database["public"]["Enums"]["frequency_unit_enum"]
            | null
          id?: string
          image_bucket_id?: string | null
          image_path?: string | null
          is_custom?: boolean | null
          med_id?: string | null
          name: string
          notes?: string | null
          opened_on_date?: string | null
          patient_id?: string | null
          prescription_id?: string | null
          price?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          dosage_amount?: number | null
          dosage_unit?: Database["public"]["Enums"]["dosage_unit_enum"] | null
          duration_amount?: number | null
          duration_unit?:
            | Database["public"]["Enums"]["duration_unit_enum"]
            | null
          expiration_date?: string | null
          frequency_amount?: number | null
          frequency_unit?:
            | Database["public"]["Enums"]["frequency_unit_enum"]
            | null
          id?: string
          image_bucket_id?: string | null
          image_path?: string | null
          is_custom?: boolean | null
          med_id?: string | null
          name?: string
          notes?: string | null
          opened_on_date?: string | null
          patient_id?: string | null
          prescription_id?: string | null
          price?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_meds_med_id_fkey"
            columns: ["med_id"]
            isOneToOne: false
            referencedRelation: "allmeds"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_meds_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_meds_prescription_id_fkey"
            columns: ["prescription_id"]
            isOneToOne: false
            referencedRelation: "prescriptions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_meds_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      blood_type_enum: "A+" | "A-" | "B+" | "B-" | "AB+" | "AB-" | "O+" | "O-"
      dosage_unit_enum:
        | "mg"
        | "g"
        | "mcg"
        | "kg"
        | "ng"
        | "IU"
        | "mEq"
        | "mmol"
        | "mL"
        | "L"
        | "tsp"
        | "tbsp"
        | "drop"
        | "cc"
        | "tablet"
        | "capsule"
        | "puff"
        | "suppository"
        | "patch"
        | "ampoule"
        | "vial"
        | "unit"
        | "spray"
        | "dropperful"
        | "lozenge"
        | "troche"
        | "film"
      duration_unit_enum: "days" | "weeks" | "months" | "Ongoing" | "As needed"
      frequency_unit_enum:
        | "daily"
        | "times daily"
        | "hours"
        | "weekly"
        | "Monthly"
        | "As needed"
        | "Before meals"
        | "After meals"
        | "With meals"
        | "At bedtime"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      blood_type_enum: ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"],
      dosage_unit_enum: [
        "mg",
        "g",
        "mcg",
        "kg",
        "ng",
        "IU",
        "mEq",
        "mmol",
        "mL",
        "L",
        "tsp",
        "tbsp",
        "drop",
        "cc",
        "tablet",
        "capsule",
        "puff",
        "suppository",
        "patch",
        "ampoule",
        "vial",
        "unit",
        "spray",
        "dropperful",
        "lozenge",
        "troche",
        "film",
      ],
      duration_unit_enum: ["days", "weeks", "months", "Ongoing", "As needed"],
      frequency_unit_enum: [
        "daily",
        "times daily",
        "hours",
        "weekly",
        "Monthly",
        "As needed",
        "Before meals",
        "After meals",
        "With meals",
        "At bedtime",
      ],
    },
  },
} as const
