# Refactor ImageCapture Component Plan

**Objective:** Decouple the image capture logic from its presentation to allow for greater layout flexibility and improved maintainability. This involves creating a custom hook (`useImageCapture`) for the core logic and separate presentational components for the UI.

---

## Progress Tracking

### Phase 1: Analysis and Design

- [x] **Identify Dependencies:** Searched codebase for all components using `ImageCapture.tsx`.
  - `components/prescriptions/PrescriptionForm.tsx`
  - `components/labresults/LabResultForm.tsx`
  - `components/medications/MedicineForm.tsx`
- [x] **Finalize Hook and Component APIs:** Defined the expected props and return values for `useImageCapture` and the new presentational components.
- [ ] **Review and Confirm Permission Handling Strategy:**
  - Current logic uses `ImagePicker.requestCameraPermissionsAsync` and `ImagePicker.requestMediaLibraryPermissionsAsync`. This is appropriate.
  - Ensure these are integrated cleanly into the `useImageCapture` hook.
  - User feedback for denied permissions must be maintained.
  - No deprecated `expo-permissions` package will be used.

### Phase 2: Implementation

- [ ] **Create `useImageCapture` Hook:**
  - File: `hooks/useImageCapture.ts`
  - Responsibility: Encapsulate all state management, permission requests, image selection (camera/library), image manipulation (resize, compress), OCR integration, and error handling.
- [ ] **Create Presentational Components:**
  - Directory: `components/common/ImageCaptureUI/`
  - [ ] `ImagePreview.tsx`: Displays the selected image, placeholder, loading/error states for preview, and overlay controls (remove/change).
  - [ ] `ImageActionButtons.tsx`: Renders "Take Photo" and "Choose from Library" buttons.
  - [ ] `ImageStatusIndicators.tsx`: Displays global loading indicators (e.g., "Processing image...") and error messages related to OCR or other processing.
- [ ] **(Optional but Recommended) Refactor `ImageCapture.tsx`:**
  - Modify the existing `components/common/ImageCapture.tsx` to use the new `useImageCapture` hook and compose the new presentational components to provide a default, ready-to-use version that maintains backward compatibility for simpler use cases or eases migration.
- [ ] **Update Consuming Components:**
  - Refactor existing forms to use `useImageCapture` and the new UI components for layout flexibility.
  - [ ] `components/prescriptions/PrescriptionForm.tsx`
  - [ ] `components/labresults/LabResultForm.tsx`
  - [ ] `components/medications/MedicineForm.tsx`

### Phase 3: Verification

- [ ] **Testing:**
  - Thoroughly test all functionalities:
    - Image picking (camera and library).
    - Image manipulation (if any specific manipulations beyond picker's quality are done by `manipulateImage` function).
    - OCR processing (if `enableOcr` is true).
    - Image removal.
    - Display of `initialImage`.
    - Error states and messages (permission denial, processing errors, OCR errors, preview load errors).
    - Image viewer functionality.
  - Confirm no regressions from current functionality.
- [ ] **Verify Permissions Configuration (iOS & Android):**
  - **iOS (`app.json` or `Info.plist`):**
    - Ensure `NSCameraUsageDescription` key is present and has a user-facing reason.
    - Ensure `NSPhotoLibraryUsageDescription` key is present and has a user-facing reason.
    - If `allowsEditing: true` is used with `launchCameraAsync`, also check for `NSPhotoLibraryAddUsageDescription` (though `expo-image-picker` might handle this by saving to a temp location).
  - **Android (`app.json` or `AndroidManifest.xml`):**
    - Ensure `<uses-permission android:name="android.permission.CAMERA" />` is present.
    - Ensure `<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />` is present. (For older Android versions).
    - Ensure `<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />` is present (For Android 13+).
    - `WRITE_EXTERNAL_STORAGE` is typically not required by `expo-image-picker` for most operations unless explicitly saving to public directories outside the app's cache. `expo-image-manipulator` also works with cache directories.
  - Confirm that permissions are requested at the appropriate time and that the user prompts are clear.

---
