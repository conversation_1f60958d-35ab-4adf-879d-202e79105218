#!/bin/bash

echo "🚀 Safe Deployment Script for AI Lab Analysis Feature"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo ""
echo -e "${YELLOW}⚠️  IMPORTANT: Database Backup${NC}"
echo "Since Docker is not running locally, please create a backup manually:"
echo "1. Go to your Supabase Dashboard"
echo "2. Navigate to Settings > Database"
echo "3. Click 'Create Backup' or download existing backup"
echo "4. Save backup with name: backup_before_ai_analysis_$(date +%Y%m%d_%H%M%S)"
echo ""
read -p "Have you created a database backup? (y/N): " backup_confirmed

if [[ ! $backup_confirmed =~ ^[Yy]$ ]]; then
    echo -e "${RED}❌ Deployment cancelled. Please create a backup first.${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Backup confirmed. Proceeding with deployment...${NC}"

# Step 1: Apply database migration
echo ""
echo "📊 Step 1: Applying database migration..."
echo "Running: supabase db push"

if supabase db push; then
    echo -e "${GREEN}✅ Database migration applied successfully${NC}"
else
    echo -e "${RED}❌ Database migration failed${NC}"
    echo "Please check the error above and fix any issues."
    exit 1
fi

# Step 2: Deploy Edge Functions
echo ""
echo "🔧 Step 2: Deploying Edge Functions..."

echo "Deploying analyze-lab-result function..."
if supabase functions deploy analyze-lab-result; then
    echo -e "${GREEN}✅ analyze-lab-result function deployed${NC}"
else
    echo -e "${RED}❌ Failed to deploy analyze-lab-result function${NC}"
    exit 1
fi

echo "Deploying chat-lab-result function..."
if supabase functions deploy chat-lab-result; then
    echo -e "${GREEN}✅ chat-lab-result function deployed${NC}"
else
    echo -e "${RED}❌ Failed to deploy chat-lab-result function${NC}"
    exit 1
fi

# Step 3: Environment Variables
echo ""
echo "🔐 Step 3: Environment Variables Setup"
echo "Please set the following secrets in your Supabase dashboard or via CLI:"
echo ""
echo "Required secrets:"
echo "- OPENAI_API_KEY=sk-your-openai-api-key"
echo "- SUPABASE_URL=https://your-project.supabase.co"
echo "- SUPABASE_SERVICE_ROLE_KEY=your-service-role-key"
echo ""
echo "CLI commands:"
echo "supabase secrets set OPENAI_API_KEY=sk-your-openai-api-key"
echo "supabase secrets set SUPABASE_URL=https://your-project.supabase.co"
echo "supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your-service-role-key"
echo ""
read -p "Have you set the environment variables? (y/N): " env_confirmed

if [[ ! $env_confirmed =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}⚠️  Please set environment variables before testing${NC}"
fi

# Step 4: Update database types
echo ""
echo "📝 Step 4: Updating database types..."
echo "Running: supabase gen types typescript --local > types/database.types.ts"

if supabase gen types typescript --local > types/database.types.ts; then
    echo -e "${GREEN}✅ Database types updated${NC}"
else
    echo -e "${YELLOW}⚠️  Could not update types (Docker not running). Update manually later.${NC}"
fi

# Step 5: Verification
echo ""
echo "🧪 Step 5: Verification Steps"
echo "Please verify the deployment:"
echo ""
echo "1. Check Supabase Dashboard > Database > Tables:"
echo "   - lab_result_analyses"
echo "   - lab_result_chat_sessions" 
echo "   - lab_result_chat_messages"
echo ""
echo "2. Check Supabase Dashboard > Edge Functions:"
echo "   - analyze-lab-result (should be deployed)"
echo "   - chat-lab-result (should be deployed)"
echo ""
echo "3. Test the feature:"
echo "   - Open a lab result with PDF attachment"
echo "   - Tap 'Analyze with AI' button"
echo "   - Verify analysis works and chat interface opens"
echo ""

# Summary
echo ""
echo -e "${GREEN}🎉 Deployment Complete!${NC}"
echo "=================================================="
echo ""
echo "✅ Database migration applied"
echo "✅ Edge Functions deployed"
echo "⚠️  Environment variables (set manually)"
echo "⚠️  Database types (update manually if needed)"
echo ""
echo "📋 Next Steps:"
echo "1. Set environment variables if not done"
echo "2. Test the AI analysis feature"
echo "3. Monitor Edge Function logs for any issues"
echo "4. Update database types if needed"
echo ""
echo "📚 Documentation:"
echo "- docs/AI_LAB_ANALYSIS_FINAL_SUMMARY.md"
echo "- docs/AI_LAB_ANALYSIS_README.md"
echo ""
echo -e "${GREEN}The AI Lab Analysis feature is now deployed! 🚀${NC}"