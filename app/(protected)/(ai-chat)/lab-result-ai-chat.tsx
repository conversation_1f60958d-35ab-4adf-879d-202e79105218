import React, { useState, useEffect, useRef } from 'react';
import { View, Text, ActivityIndicator, FlatList, Alert } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useLabResultAI, type ChatMessage } from '@/hooks/useLabResultAI';
import ChatMessageComponent from '@/components/chat/ChatMessage';
import ChatInput from '@/components/chat/ChatInput';
import SuggestedQuestions from '@/components/chat/SuggestedQuestions';
import { Button } from '@/components/ui/Button';

export default function LabResultAIChatScreen() {
  const { labResultId } = useLocalSearchParams<{ labResultId: string }>();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<any>(null);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [isStreaming, setIsStreaming] = useState(false);

  const flatListRef = useRef<FlatList>(null);

  const {
    analyzeLabResult,
    sendChatMessage,
    useFetchAnalysis,
    useFetchChatHistory
  } = useLabResultAI();

  // Fetch existing analysis
  const { data: existingAnalysis, isLoading: isLoadingAnalysis } = useFetchAnalysis(labResultId);

  // Fetch chat history
  const { data: chatHistory, isLoading: isLoadingHistory } = useFetchChatHistory(labResultId);

  useEffect(() => {
    if (existingAnalysis) {
      setAnalysis(existingAnalysis);
      // Add initial AI message if no chat history
      if (!chatHistory || chatHistory.length === 0) {
        const initialMessage: ChatMessage = {
          id: 'initial',
          role: 'assistant',
          content: `I've analyzed your lab results. Here's what I found:\n\n${existingAnalysis.ai_analysis.summary}\n\nFeel free to ask me any questions about your results!`,
          timestamp: new Date(),
        };
        setMessages([initialMessage]);
      }
    } else if (!isLoadingAnalysis) {
      // No existing analysis, trigger new analysis
      handleAnalyze();
    }
  }, [existingAnalysis, isLoadingAnalysis]);

  useEffect(() => {
    if (chatHistory && chatHistory.length > 0) {
      setMessages(chatHistory);
      setShowSuggestions(false);
    }
  }, [chatHistory]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const handleAnalyze = async () => {
    setIsAnalyzing(true);
    try {
      const result = await analyzeLabResult.mutateAsync(labResultId);
      setAnalysis(result.analysis);

      // Add initial AI message with analysis summary
      const initialMessage: ChatMessage = {
        id: 'initial',
        role: 'assistant',
        content: `I've analyzed your lab results. Here's what I found:\n\n${result.analysis.ai_analysis.summary}\n\nFeel free to ask me any questions about your results!`,
        timestamp: new Date(),
      };
      setMessages([initialMessage]);
    } catch (error) {
      console.error('Analysis failed:', error);
      Alert.alert(
        'Analysis Failed',
        'Unable to analyze your lab results. Please try again later.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: handleAnalyze }
        ]
      );
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSendMessage = async (userMessage: string) => {
    if (!analysis) {
      Alert.alert('Error', 'Lab results must be analyzed first');
      return;
    }

    // Add user message immediately
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: userMessage,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, newUserMessage]);
    setShowSuggestions(false);

    const aiMessageId = `ai-${Date.now()}`;

    try {
      setIsStreaming(true);

      // Add placeholder message for AI response
      const placeholderMessage: ChatMessage = {
        id: aiMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, placeholderMessage]);

      // Get response from sendChatMessage
      const response = await sendChatMessage(labResultId, userMessage, messages);

      // Parse the response content
      const responseText = await response.text();
      const data = JSON.parse(responseText);

      const assistantMessage = data.content || '';

      if (!assistantMessage) {
        throw new Error('No content received from AI');
      }

      // Update the placeholder message with the actual content
      setMessages(prev => prev.map(msg =>
        msg.id === aiMessageId ? { ...msg, content: assistantMessage } : msg
      ));

    } catch (error) {
      console.error('Chat error:', error);
      // Remove the placeholder message on error
      setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setIsStreaming(false);
    }
  };

  const handleQuestionPress = (question: string) => {
    handleSendMessage(question);
  };

  if (isLoadingAnalysis || isLoadingHistory) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <Stack.Screen options={{ title: 'AI Lab Analysis' }} />
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">
          Loading...
        </Text>
      </SafeAreaView>
    );
  }

  if (isAnalyzing) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <Stack.Screen options={{ title: 'AI Lab Analysis' }} />
        <View className="items-center">
          <View className="w-16 h-16 rounded-full bg-primary/10 dark:bg-sky-400/10 items-center justify-center mb-4">
            <Ionicons name="sparkles" size={32} className="text-primary dark:text-sky-400" />
          </View>
          <ActivityIndicator size="large" className="text-primary mb-4" />
          <Text className="text-lg font-medium text-foreground dark:text-neutral-200 mb-2">
            Analyzing your lab results
          </Text>
          <Text className="text-muted-foreground dark:text-neutral-400 text-center px-8">
            Our AI is reviewing your lab data to provide insights and answer your questions.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
      <Stack.Screen
        options={{
          title: 'AI Lab Analysis',
          headerRight: () => (
            <Button
              title="Close"
              onPress={() => router.back()}
              variant="outline"
            />
          )
        }}
      />

      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={({ item }) => <ChatMessageComponent message={item} />}
        keyExtractor={item => item.id}
        className="flex-1"
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View className="flex-1 justify-center items-center py-20">
            <Ionicons name="chatbubbles-outline" size={48} className="text-muted-foreground dark:text-neutral-500 mb-4" />
            <Text className="text-lg font-medium text-muted-foreground dark:text-neutral-400 mb-2">
              No messages yet
            </Text>
            <Text className="text-muted-foreground dark:text-neutral-500 text-center px-8">
              Start by asking a question about your lab results
            </Text>
          </View>
        }
      />

      {isStreaming && (
        <View className="px-4 py-2 border-t border-border dark:border-neutral-700 bg-muted/30 dark:bg-neutral-800/30">
          <View className="flex-row items-center">
            <ActivityIndicator size="small" className="text-primary mr-2" />
            <Text className="text-sm text-muted-foreground dark:text-neutral-400">
              AI is typing...
            </Text>
          </View>
        </View>
      )}

      {showSuggestions && messages.length <= 1 && (
        <SuggestedQuestions
          onQuestionPress={handleQuestionPress}
          disabled={isStreaming}
        />
      )}

      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isStreaming || !analysis}
        placeholder={analysis ? "Ask about your lab results..." : "Analyzing results..."}
      />
    </SafeAreaView>
  );
}