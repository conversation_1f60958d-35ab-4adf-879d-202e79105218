import { useState, useCallback } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useSupabaseClient } from './useSupabaseClient';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface LabAnalysis {
  id: string;
  lab_result_id: string;
  ai_analysis: {
    summary: string;
    keyFindings: string[];
    recommendations: string[];
    riskFactors: string[];
    timestamp: string;
    model_used: string;
  };
  created_at: string;
}

export function useLabResultAI() {
  const { supabase } = useSupabaseClient();

  // Mutation for analyzing lab result
  const analyzeLabResult = useMutation({
    mutationFn: async (labResultId: string): Promise<{ analysis: LabAnalysis; cached: boolean }> => {
      if (!supabase) {
        throw new Error('Supabase client not available');
      }

      try {
        const { data, error } = await supabase.functions.invoke('analyze-lab-result', {
          body: { labResultId }
        });

        if (error) {
          console.error('Supabase function error:', error);
          console.error('Error details:', JSON.stringify(error, null, 2));

          // Try to extract more details from the error
          if (error.context && error.context.body) {
            console.error('Error response body:', error.context.body);
            try {
              const errorBody = JSON.parse(error.context.body);
              throw new Error(errorBody.error || error.message || 'Failed to analyze lab result');
            } catch (parseError) {
              console.error('Could not parse error body:', parseError);
            }
          }

          throw new Error(error.message || 'Failed to analyze lab result');
        }

        // Check if the response contains an error (from our improved error handling)
        if (data && data.error) {
          console.error('Function returned error:', data);
          throw new Error(data.error || 'Analysis failed');
        }

        // Check for success field (backward compatibility)
        if (data && data.success === false) {
          console.error('Function returned failure:', data);
          throw new Error(data.error || 'Analysis failed');
        }

        return data;
      } catch (err) {
        console.error('Full error object:', err);
        throw err;
      }
    },
  });

  // Query for fetching existing analysis
  const useFetchAnalysis = (labResultId: string) => {
    return useQuery({
      queryKey: ['lab-analysis', labResultId],
      queryFn: async (): Promise<LabAnalysis | null> => {
        if (!supabase) {
          throw new Error('Supabase client not available');
        }

        const { data, error } = await (supabase as any)
          .from('lab_result_analyses')
          .select('*')
          .eq('lab_result_id', labResultId)
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
          throw error;
        }

        return data as LabAnalysis | null;
      },
      enabled: !!labResultId && !!supabase,
    });
  };

  // Function for sending chat messages with streaming
  const sendChatMessage = useCallback(async (
    labResultId: string,
    message: string,
    chatHistory: ChatMessage[] = []
  ): Promise<Response> => {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }

    // Use supabase.functions.invoke but get the raw response for streaming
    const response = await supabase.functions.invoke('chat-lab-result', {
      body: {
        labResultId,
        message,
        chatHistory: chatHistory.map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      }
    });

    if (response.error) {
      console.error('Chat function error:', response.error);
      throw new Error(response.error.message || 'Failed to send chat message');
    }

    // Return the response data wrapped in a Response-like object for compatibility
    return new Response(JSON.stringify(response.data), {
      headers: { 'Content-Type': 'application/json' }
    });
  }, [supabase]);

  // Query for fetching chat history
  const useFetchChatHistory = (labResultId: string) => {
    return useQuery({
      queryKey: ['chat-history', labResultId],
      queryFn: async (): Promise<ChatMessage[]> => {
        if (!supabase) {
          throw new Error('Supabase client not available');
        }

        // First get the session
        const { data: session } = await (supabase as any)
          .from('lab_result_chat_sessions')
          .select('id')
          .eq('lab_result_id', labResultId)
          .single();

        if (!session) {
          return [];
        }

        // Then get the messages
        const { data: messages, error } = await (supabase as any)
          .from('lab_result_chat_messages')
          .select('*')
          .eq('session_id', session.id)
          .order('created_at', { ascending: true });

        if (error) {
          throw error;
        }

        return messages?.map((msg: any) => ({
          id: msg.id,
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: new Date(msg.created_at),
        })) || [];
      },
      enabled: !!labResultId && !!supabase,
    });
  };

  return {
    analyzeLabResult,
    sendChatMessage,
    useFetchAnalysis,
    useFetchChatHistory,
  };
}

// Simplified hook for chat responses (no streaming)
export function useChatResponse() {
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState('');
  const [error, setError] = useState<Error | null>(null);

  const getChatResponse = useCallback(async (
    chatPromise: Promise<any>,
    onComplete?: (fullResponse: string) => void
  ) => {
    setIsLoading(true);
    setResponse('');
    setError(null);
    try {
      const data = await chatPromise;
      if (data && data.content) {
        setResponse(data.content);
        if (onComplete) onComplete(data.content);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Chat failed'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    getChatResponse,
    isLoading,
    response,
    error,
  };
}