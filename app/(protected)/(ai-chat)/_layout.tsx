import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Drawer } from 'expo-router/drawer';
import { useTheme } from '@react-navigation/native';

export default function ChatLayout() {
  const theme = useTheme();

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        screenOptions={{
          drawerStyle: {
            backgroundColor: theme.colors.card,
            width: 280,
          },
          headerStyle: {
            backgroundColor: theme.colors.card,
          },
          headerTintColor: theme.colors.text,
          drawerActiveTintColor: theme.colors.primary,
          drawerInactiveTintColor: theme.colors.text,
        }}
      >
        <Drawer.Screen
          name="index"
          options={{
            drawerLabel: 'New Chat',
            title: 'AI Lab Analysis',
          }}
        />
      </Drawer>
    </GestureHandlerRootView>
  );
}